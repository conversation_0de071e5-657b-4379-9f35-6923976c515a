import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar, StyleSheet, Text, TextInput } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { withIAPContext } from "react-native-iap";
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from "react-native-reanimated";
import { useNotifications } from "../lib/hooks/use-notifications";
import { useSplashScreen } from "../lib/hooks/useSplashScreen";
import { useSyncIAPWithServer } from "../lib/hooks/useSyncIAPWithServer";
import { GlobalModalProvider } from "../ui/common/GlobalModalProvider";

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

SplashScreen.setOptions({
  duration: 500,
  fade: true
})

// This is the default configuration
configureReanimatedLogger({
  level: ReanimatedLogLevel.error,
  strict: false, // Reanimated runs in strict mode by default
});

(TextInput as any).defaultProps = {
  allowFontScaling: false,
  maxFontSizeMultiplier: 1,
};

(Text as any).defaultProps = {
  allowFontScaling: false,
  maxFontSizeMultiplier: 1,
};

function RootLayout() {
  const { onLayoutRootView } = useSplashScreen();
  useNotifications();
  useSyncIAPWithServer();

  return (
    <GestureHandlerRootView style={styles.container} onLayout={onLayoutRootView}>
      <StatusBar barStyle="default" />
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="index" />
        <Stack.Screen name="home" />
        <Stack.Screen name="daily-itinerary" />
        <Stack.Screen name="interests" />
        <Stack.Screen name="costs"
          options={{
            presentation: "modal",
          }}
        />
        <Stack.Screen name="processing" />
        <Stack.Screen name="trip-confirmation" />
        <Stack.Screen
          name="share"
          options={{
            presentation: "modal",
          }}
        />
        <Stack.Screen name="trip-details" />
        <Stack.Screen name="settings" />
        <Stack.Screen name="trip-view" />
        <Stack.Screen
          name="city-map"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          options={{
            presentation: "modal",
          }}
          name="account"
        />
        <Stack.Screen
          options={{
            presentation: "modal",
          }}
          name="credits-dashboard"
        />
        <Stack.Screen
          options={{
            presentation: "modal",
          }}
          name="buy-credits"
        />
        <Stack.Screen
          options={{
            presentation: "modal",
          }}
          name="usage-history"
        />
        <Stack.Screen name="login" />
        <Stack.Screen
          name="delete-account"
          options={{
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="country-trips"
          options={{
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="activities-list"
          options={{
            presentation: "modal",
          }}
        />
      </Stack>

      {/* Global modals that should be accessible from anywhere in the app */}
      <GlobalModalProvider />
    </GestureHandlerRootView>

  );
}

export default withIAPContext(RootLayout);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
