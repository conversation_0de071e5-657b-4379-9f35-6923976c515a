import React, { useMemo, useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { MapView } from "../map/MapUtils";
import type { Region } from "../map/MapUtils";
import { Activity } from "../../lib/types";
import MapMarker from "../map/MapMarker";
import DraggableActivityList from "./DraggableActivityList";
import Modal from "../common/Modal";
import { useSafeAreaInsets } from "react-native-safe-area-context";
interface CustomizeItineraryProps {
  activities: Activity[];
  selectedDay: number;
}

export default function CustomizeItinerary({
  activities,
  selectedDay,
}: CustomizeItineraryProps) {
  const [selectedActivities, setSelectedActivities] =
    useState<Activity[]>(activities);
  const [listModalVisible, setListModalVisible] = useState(true);
  const [activityModalVisible, setActivityModalVisible] = useState(false);
  const [currentActivity, setCurrentActivity] = useState<Activity | null>(null);
  const { bottom } = useSafeAreaInsets();
  const initialRegion: Region = useMemo(() => {
    return {
      latitude: activities[0].coordinates?.lat ?? 0,
      longitude: activities[0].coordinates?.lng ?? 0,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
  }, [activities]);

  // Handle activity selection
  const handleSelectActivity = (activity) => {
    setSelectedActivities([...selectedActivities, activity]);
    setActivityModalVisible(false);
  };

  // Handle saving and navigating to next day
  const handleSaveAndNext = () => {
    // Save to context or local storage here if needed
  };

  const handleMarkerPress = (activity: Activity) => () => {
    setCurrentActivity(activity);
    setActivityModalVisible(true);
  };

  return (
    <View style={{ flex: 1 }}>
      {/* Map Section */}
      <MapView styleURL="mapbox://styles/viguize/cmdhpo9d7000g01sk5fx9b0n6" style={{ flex: 2 }} initialRegion={initialRegion}>
        {activities.map((activity, index) => (
          <MapMarker
            key={activity.id}
            activity={activity}
            onPress={handleMarkerPress(activity)}
          />
        ))}
      </MapView>

      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          paddingBottom: bottom,
        }}
      >
        <Text style={{ fontSize: 18, fontWeight: "bold" }}>
          Selected Activities for Day {selectedDay}
        </Text>
      </View>

      {/* Activity List */}
      <Modal
        visible={listModalVisible}
        onClose={() => setListModalVisible(false)}
      >
        <Text style={{ fontSize: 18, fontWeight: "bold" }}>
          Selected Activities for Day {selectedDay}
        </Text>

        <DraggableActivityList
          activities={selectedActivities}
          onDragEnd={() => { }}
          onEditActivity={() => { }}
          onRemoveActivity={() => { }}
        />
        <TouchableOpacity
          style={{
            padding: 15,
            backgroundColor: "#007AFF",
            alignItems: "center",
            borderRadius: 10,
            margin: 10,
            marginBottom: bottom + 10,
          }}
          onPress={handleSaveAndNext}
        >
          <Text style={{ color: "white", fontSize: 16 }}>Save & Next Day</Text>
        </TouchableOpacity>
      </Modal>

      {/* Save and Next Button */}

      {/* Activity Modal */}
      <Modal
        visible={activityModalVisible}
        onClose={() => setActivityModalVisible(false)}
      >
        <View
          style={{ backgroundColor: "white", padding: 20, borderRadius: 10 }}
        >
          <Text style={{ fontSize: 18, fontWeight: "bold" }}>
            {currentActivity?.name}
          </Text>
          <Text>Duration: {currentActivity?.duration}</Text>
          <TouchableOpacity
            onPress={() => handleSelectActivity(currentActivity)}
            style={{
              backgroundColor: "#28A745",
              padding: 10,
              borderRadius: 5,
              marginTop: 10,
            }}
          >
            <Text style={{ color: "white" }}>Add to Itinerary</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setActivityModalVisible(false)}
            style={{
              backgroundColor: "#DC3545",
              padding: 10,
              borderRadius: 5,
              marginTop: 10,
            }}
          >
            <Text style={{ color: "white" }}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}
