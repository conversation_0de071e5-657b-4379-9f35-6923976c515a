export interface RenewalFailureData {
  userName: string;
  planName: string;
  amount: number;
  failureDate: string;
  retryDate?: string;
  gracePeriodEnd?: string;
  reason?: string;
}

export function generateRenewalFailureHtml(data: RenewalFailureData): string {
  const { userName, planName, amount, failureDate, retryDate, gracePeriodEnd, reason } = data;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Subscription Renewal Failed - Trip Itinerary Planner</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); padding: 40px 30px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">
            ⚠️ Renewal Failed
          </h1>
          <p style="color: #fed7d7; margin: 10px 0 0 0; font-size: 16px;">
            We couldn't process your ${planName} subscription renewal
          </p>
        </div>

        <!-- Content -->
        <div style="padding: 40px 30px;">
          
          <!-- Greeting -->
          <div style="margin-bottom: 30px;">
            <h2 style="color: #1a202c; margin: 0 0 15px 0; font-size: 24px; font-weight: 600;">
              Hello ${userName},
            </h2>
            <p style="color: #4a5568; margin: 0; font-size: 16px; line-height: 1.6;">
              We encountered an issue while trying to renew your subscription. Don't worry - we're here to help you resolve this quickly.
            </p>
          </div>

          <!-- Failure Details -->
          <div style="background-color: #fef5e7; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #f6ad55;">
            <h3 style="color: #2d3748; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
              Renewal Details
            </h3>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Plan:</span>
              <span style="color: #2d3748; font-weight: 600;">${planName} Subscription</span>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Amount:</span>
              <span style="color: #2d3748; font-weight: 600;">$${amount.toFixed(2)} USD</span>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Failure Date:</span>
              <span style="color: #2d3748;">${failureDate}</span>
            </div>
            
            ${reason ? `
            <div style="display: flex; justify-content: space-between;">
              <span style="color: #4a5568; font-weight: 500;">Reason:</span>
              <span style="color: #e53e3e; font-weight: 500;">${reason}</span>
            </div>
            ` : ''}
          </div>

          ${gracePeriodEnd ? `
          <!-- Grace Period Notice -->
          <div style="background-color: #fff5f5; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #f56565;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              ⏰ Grace Period Active
            </h3>
            <p style="color: #4a5568; margin: 0 0 15px 0; font-size: 16px;">
              Your subscription is currently in a grace period. You can continue using your plan until <strong>${gracePeriodEnd}</strong>.
            </p>
            <p style="color: #e53e3e; margin: 0; font-size: 14px; font-weight: 500;">
              Please update your payment method before this date to avoid service interruption.
            </p>
          </div>
          ` : ''}

          ${retryDate ? `
          <!-- Retry Information -->
          <div style="background-color: #edf2f7; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #4299e1;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              🔄 Automatic Retry
            </h3>
            <p style="color: #4a5568; margin: 0; font-size: 16px;">
              We'll automatically try to process your payment again on <strong>${retryDate}</strong>.
            </p>
          </div>
          ` : ''}

          <!-- Action Required -->
          <div style="background-color: #f0fff4; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #48bb78;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              🔧 How to Fix This
            </h3>
            
            <div style="margin-bottom: 15px;">
              <h4 style="color: #2d3748; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">
                1. Check Your Payment Method
              </h4>
              <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
                Ensure your credit card hasn't expired and has sufficient funds available.
              </p>
            </div>
            
            <div style="margin-bottom: 15px;">
              <h4 style="color: #2d3748; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">
                2. Update Payment Information
              </h4>
              <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
                Open the Trip Itinerary Planner app and go to Settings > Subscription to update your payment method.
              </p>
            </div>
            
            <div>
              <h4 style="color: #2d3748; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">
                3. Contact Support
              </h4>
              <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
                If you continue to experience issues, our support team is ready to help at 
                <a href="mailto:<EMAIL>" style="color: #4299e1; text-decoration: none;"><EMAIL></a>
              </p>
            </div>
          </div>

          <!-- Common Reasons -->
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h4 style="color: #2d3748; margin: 0 0 15px 0; font-size: 16px; font-weight: 600;">
              💡 Common Reasons for Payment Failures
            </h4>
            <ul style="color: #4a5568; margin: 0; padding-left: 20px; font-size: 14px; line-height: 1.6;">
              <li>Expired credit card</li>
              <li>Insufficient funds</li>
              <li>Bank declined the transaction</li>
              <li>Outdated billing address</li>
              <li>Card issuer security restrictions</li>
            </ul>
          </div>

          <!-- Support -->
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h4 style="color: #2d3748; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">
              Need Immediate Help?
            </h4>
            <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
              Our support team is available to assist you with payment issues. Contact us at 
              <a href="mailto:<EMAIL>" style="color: #4299e1; text-decoration: none;"><EMAIL></a> 
              and include your transaction details for faster assistance.
            </p>
          </div>

        </div>

        <!-- Footer -->
        <div style="background-color: #2d3748; padding: 30px; text-align: center;">
          <p style="color: #a0aec0; margin: 0 0 15px 0; font-size: 14px;">
            We're here to help resolve this issue quickly
          </p>
          
          <div style="margin-bottom: 20px;">
            <a href="https://www.aiplanmytrip.com" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Website</a>
            <a href="https://www.aiplanmytrip.com/help" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Help Center</a>
            <a href="https://www.aiplanmytrip.com/contact" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Contact</a>
          </div>
          
          <p style="color: #718096; margin: 0; font-size: 12px;">
            © 2024 Trip Itinerary Planner. All rights reserved.
          </p>
          
          <p style="color: #718096; margin: 10px 0 0 0; font-size: 11px;">
            <a href="https://www.aiplanmytrip.com/privacy" style="color: #718096; text-decoration: none;">Privacy Policy</a> | 
            <a href="https://www.aiplanmytrip.com/terms" style="color: #718096; text-decoration: none;">Terms of Service</a>
          </p>
        </div>

      </div>
    </body>
    </html>
  `;
}

export function generateRenewalFailureText(data: RenewalFailureData): string {
  const { userName, planName, amount, failureDate, retryDate, gracePeriodEnd, reason } = data;

  return `
Trip Itinerary Planner - Subscription Renewal Failed

Hello ${userName},

⚠️ We encountered an issue while trying to renew your subscription. Don't worry - we're here to help you resolve this quickly.

Renewal Details:
- Plan: ${planName} Subscription
- Amount: $${amount.toFixed(2)} USD
- Failure Date: ${failureDate}
${reason ? `- Reason: ${reason}` : ''}

${gracePeriodEnd ? `
⏰ Grace Period Active:
Your subscription is currently in a grace period. You can continue using your plan until ${gracePeriodEnd}.
Please update your payment method before this date to avoid service interruption.
` : ''}

${retryDate ? `
🔄 Automatic Retry:
We'll automatically try to process your payment again on ${retryDate}.
` : ''}

🔧 How to Fix This:

1. Check Your Payment Method
   Ensure your credit card hasn't expired and has sufficient funds available.

2. Update Payment Information
   Open the Trip Itinerary Planner app and go to Settings > Subscription to update your payment method.

3. Contact Support
   If you continue to experience issues, our support team is ready to <NAME_EMAIL>

💡 Common Reasons for Payment Failures:
- Expired credit card
- Insufficient funds
- Bank declined the transaction
- Outdated billing address
- Card issuer security restrictions

Need Immediate Help?
Our support team is available to assist you with payment issues. Contact <NAME_EMAIL> and include your transaction details for faster assistance.

We're here to help resolve this issue quickly.

Visit us at: https://www.aiplanmytrip.com
Help Center: https://www.aiplanmytrip.com/help
Contact Us: https://www.aiplanmytrip.com/contact

© 2024 Trip Itinerary Planner. All rights reserved.

Privacy Policy: https://www.aiplanmytrip.com/privacy
Terms of Service: https://www.aiplanmytrip.com/terms
  `;
}
