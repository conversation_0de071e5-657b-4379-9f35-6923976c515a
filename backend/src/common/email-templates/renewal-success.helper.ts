export interface RenewalSuccessData {
  userName: string;
  planName: string;
  amount: number;
  daysPerMonth: number;
  renewalDate: string;
  nextBillingDate: string;
  transactionId: string;
}

export function generateRenewalSuccessHtml(data: RenewalSuccessData): string {
  const { userName, planName, amount, daysPerMonth, renewalDate, nextBillingDate, transactionId } = data;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Subscription Renewed - Trip Itinerary Planner</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); padding: 40px 30px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">
            ✅ Subscription Renewed!
          </h1>
          <p style="color: #c6f6d5; margin: 10px 0 0 0; font-size: 16px;">
            Your ${planName} plan has been successfully renewed
          </p>
        </div>

        <!-- Content -->
        <div style="padding: 40px 30px;">
          
          <!-- Greeting -->
          <div style="margin-bottom: 30px;">
            <h2 style="color: #1a202c; margin: 0 0 15px 0; font-size: 24px; font-weight: 600;">
              Hello ${userName}!
            </h2>
            <p style="color: #4a5568; margin: 0; font-size: 16px; line-height: 1.6;">
              Great news! Your subscription has been automatically renewed and your travel planning days have been refreshed.
            </p>
          </div>

          <!-- Renewal Details -->
          <div style="background-color: #f0fff4; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #48bb78;">
            <h3 style="color: #2d3748; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
              Renewal Details
            </h3>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Plan:</span>
              <span style="color: #2d3748; font-weight: 600;">${planName} Subscription</span>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Amount:</span>
              <span style="color: #48bb78; font-weight: 700; font-size: 18px;">$${amount.toFixed(2)} USD</span>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Renewal Date:</span>
              <span style="color: #2d3748;">${renewalDate}</span>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Next Billing:</span>
              <span style="color: #2d3748;">${nextBillingDate}</span>
            </div>
            
            <div style="display: flex; justify-content: space-between;">
              <span style="color: #4a5568; font-weight: 500;">Transaction ID:</span>
              <span style="color: #2d3748; font-family: monospace; font-size: 14px;">${transactionId}</span>
            </div>
          </div>

          <!-- Plan Benefits -->
          <div style="background-color: #edf2f7; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #4299e1;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              📅 Your Plan Benefits
            </h3>
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
              <span style="font-size: 20px; margin-right: 10px;">⚡</span>
              <span style="color: #4a5568; font-size: 16px;">
                <strong>${daysPerMonth} days per month</strong> for trip planning
              </span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
              <span style="font-size: 20px; margin-right: 10px;">🤖</span>
              <span style="color: #4a5568; font-size: 16px;">
                AI-powered itinerary generation
              </span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
              <span style="font-size: 20px; margin-right: 10px;">🗺️</span>
              <span style="color: #4a5568; font-size: 16px;">
                Interactive maps and navigation
              </span>
            </div>
            <div style="display: flex; align-items: center;">
              <span style="font-size: 20px; margin-right: 10px;">💎</span>
              <span style="color: #4a5568; font-size: 16px;">
                Hidden gems and local recommendations
              </span>
            </div>
          </div>

          <!-- Receipt Attachment -->
          <div style="background-color: #fffaf0; border-radius: 8px; padding: 20px; margin-bottom: 30px; border: 1px solid #fed7aa;">
            <div style="display: flex; align-items: center;">
              <span style="font-size: 24px; margin-right: 15px;">📄</span>
              <div>
                <h4 style="color: #9a3412; margin: 0 0 5px 0; font-size: 16px; font-weight: 600;">
                  Receipt Attached
                </h4>
                <p style="color: #a16207; margin: 0; font-size: 14px;">
                  Your detailed renewal receipt is attached to this email as a PDF file.
                </p>
              </div>
            </div>
          </div>

          <!-- Continue Planning -->
          <div style="text-align: center; margin-bottom: 30px; padding: 25px; background-color: #f7fafc; border-radius: 8px;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              🚀 Ready for Your Next Adventure?
            </h3>
            <p style="color: #4a5568; margin: 0 0 20px 0; font-size: 16px; line-height: 1.6;">
              Your days have been refreshed! Start planning your next amazing trip with our AI-powered assistant.
            </p>
          </div>

          <!-- Manage Subscription -->
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h4 style="color: #2d3748; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">
              📱 Manage Your Subscription
            </h4>
            <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
              You can manage your subscription, update payment methods, or view billing history directly in the app settings.
            </p>
          </div>

          <!-- Support -->
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h4 style="color: #2d3748; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">
              Need Help?
            </h4>
            <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
              If you have any questions about your subscription or need assistance, please contact our support team at 
              <a href="mailto:<EMAIL>" style="color: #4299e1; text-decoration: none;"><EMAIL></a>
            </p>
          </div>

        </div>

        <!-- Footer -->
        <div style="background-color: #2d3748; padding: 30px; text-align: center;">
          <p style="color: #a0aec0; margin: 0 0 15px 0; font-size: 14px;">
            Thank you for being a valued subscriber!
          </p>
          
          <div style="margin-bottom: 20px;">
            <a href="https://www.aiplanmytrip.com" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Website</a>
            <a href="https://www.aiplanmytrip.com/help" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Help Center</a>
            <a href="https://www.aiplanmytrip.com/contact" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Contact</a>
          </div>
          
          <p style="color: #718096; margin: 0; font-size: 12px;">
            © 2024 Trip Itinerary Planner. All rights reserved.
          </p>
          
          <p style="color: #718096; margin: 10px 0 0 0; font-size: 11px;">
            <a href="https://www.aiplanmytrip.com/privacy" style="color: #718096; text-decoration: none;">Privacy Policy</a> | 
            <a href="https://www.aiplanmytrip.com/terms" style="color: #718096; text-decoration: none;">Terms of Service</a>
          </p>
        </div>

      </div>
    </body>
    </html>
  `;
}

export function generateRenewalSuccessText(data: RenewalSuccessData): string {
  const { userName, planName, amount, daysPerMonth, renewalDate, nextBillingDate, transactionId } = data;

  return `
Trip Itinerary Planner - Subscription Renewed

Hello ${userName}!

✅ Great news! Your subscription has been automatically renewed and your travel planning days have been refreshed.

Renewal Details:
- Plan: ${planName} Subscription
- Amount: $${amount.toFixed(2)} USD
- Renewal Date: ${renewalDate}
- Next Billing: ${nextBillingDate}
- Transaction ID: ${transactionId}

📅 Your Plan Benefits:
⚡ ${daysPerMonth} days per month for trip planning
🤖 AI-powered itinerary generation
🗺️ Interactive maps and navigation
💎 Hidden gems and local recommendations

📄 Receipt Attached:
Your detailed renewal receipt is attached to this email as a PDF file.

🚀 Ready for Your Next Adventure?
Your days have been refreshed! Start planning your next amazing trip with our AI-powered assistant.

📱 Manage Your Subscription:
You can manage your subscription, update payment methods, or view billing history directly in the app settings.

Need Help?
If you have any questions about your subscription or need assistance, please contact our support <NAME_EMAIL>

Thank you for being a valued subscriber!

Visit us at: https://www.aiplanmytrip.com
Help Center: https://www.aiplanmytrip.com/help
Contact Us: https://www.aiplanmytrip.com/contact

© 2024 Trip Itinerary Planner. All rights reserved.

Privacy Policy: https://www.aiplanmytrip.com/privacy
Terms of Service: https://www.aiplanmytrip.com/terms
  `;
}
