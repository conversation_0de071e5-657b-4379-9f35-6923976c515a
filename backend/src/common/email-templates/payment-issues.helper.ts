export interface PaymentIssuesData {
  userName: string;
  planName: string;
  issueType: 'grace_period_expired' | 'payment_declined' | 'subscription_cancelled' | 'refund_processed';
  issueDate: string;
  amount?: number;
  transactionId?: string;
  nextAction?: string;
  supportMessage?: string;
}

export function generatePaymentIssuesHtml(data: PaymentIssuesData): string {
  const { userName, planName, issueType, issueDate, amount, transactionId, nextAction, supportMessage } = data;

  const getIssueTitle = () => {
    switch (issueType) {
      case 'grace_period_expired':
        return '⏰ Grace Period Expired';
      case 'payment_declined':
        return '❌ Payment Declined';
      case 'subscription_cancelled':
        return '🚫 Subscription Cancelled';
      case 'refund_processed':
        return '💰 Refund Processed';
      default:
        return '⚠️ Payment Issue';
    }
  };

  const getIssueDescription = () => {
    switch (issueType) {
      case 'grace_period_expired':
        return 'Your subscription grace period has expired and your account has been downgraded to the free plan.';
      case 'payment_declined':
        return 'Your payment was declined by your bank or card issuer.';
      case 'subscription_cancelled':
        return 'Your subscription has been cancelled and you\'ve been moved to the free plan.';
      case 'refund_processed':
        return 'A refund has been processed for your recent purchase.';
      default:
        return 'We encountered an issue with your payment or subscription.';
    }
  };

  const getHeaderColor = () => {
    switch (issueType) {
      case 'refund_processed':
        return 'background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);';
      case 'subscription_cancelled':
        return 'background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);';
      default:
        return 'background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);';
    }
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Payment Issue - Trip Itinerary Planner</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        
        <!-- Header -->
        <div style="${getHeaderColor()} padding: 40px 30px; text-align: center;">
          <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">
            ${getIssueTitle()}
          </h1>
          <p style="color: rgba(255, 255, 255, 0.8); margin: 10px 0 0 0; font-size: 16px;">
            ${planName} Subscription
          </p>
        </div>

        <!-- Content -->
        <div style="padding: 40px 30px;">
          
          <!-- Greeting -->
          <div style="margin-bottom: 30px;">
            <h2 style="color: #1a202c; margin: 0 0 15px 0; font-size: 24px; font-weight: 600;">
              Hello ${userName},
            </h2>
            <p style="color: #4a5568; margin: 0; font-size: 16px; line-height: 1.6;">
              ${getIssueDescription()}
            </p>
          </div>

          <!-- Issue Details -->
          <div style="background-color: #fef5e7; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #f6ad55;">
            <h3 style="color: #2d3748; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">
              Issue Details
            </h3>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Plan:</span>
              <span style="color: #2d3748; font-weight: 600;">${planName} Subscription</span>
            </div>
            
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Issue Date:</span>
              <span style="color: #2d3748;">${issueDate}</span>
            </div>
            
            ${amount ? `
            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
              <span style="color: #4a5568; font-weight: 500;">Amount:</span>
              <span style="color: #2d3748; font-weight: 600;">$${amount.toFixed(2)} USD</span>
            </div>
            ` : ''}
            
            ${transactionId ? `
            <div style="display: flex; justify-content: space-between;">
              <span style="color: #4a5568; font-weight: 500;">Transaction ID:</span>
              <span style="color: #2d3748; font-family: monospace; font-size: 14px;">${transactionId}</span>
            </div>
            ` : ''}
          </div>

          ${issueType === 'grace_period_expired' ? `
          <!-- Free Plan Notice -->
          <div style="background-color: #edf2f7; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #4299e1;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              📱 You're Now on the Free Plan
            </h3>
            <p style="color: #4a5568; margin: 0 0 15px 0; font-size: 16px;">
              You can still use Trip Itinerary Planner with 5 days per month on the free plan.
            </p>
            <p style="color: #4a5568; margin: 0; font-size: 14px;">
              To restore your full subscription benefits, please update your payment method and resubscribe.
            </p>
          </div>
          ` : ''}

          ${issueType === 'refund_processed' ? `
          <!-- Refund Notice -->
          <div style="background-color: #f0fff4; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #48bb78;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              💰 Refund Information
            </h3>
            <p style="color: #4a5568; margin: 0 0 15px 0; font-size: 16px;">
              Your refund has been processed and should appear in your account within 3-5 business days.
            </p>
            <p style="color: #4a5568; margin: 0; font-size: 14px;">
              The refund amount will be credited to the original payment method used for the purchase.
            </p>
          </div>
          ` : ''}

          ${nextAction ? `
          <!-- Next Steps -->
          <div style="background-color: #f0fff4; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #48bb78;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              🔧 What You Can Do
            </h3>
            <p style="color: #4a5568; margin: 0; font-size: 16px; line-height: 1.6;">
              ${nextAction}
            </p>
          </div>
          ` : ''}

          ${issueType === 'payment_declined' ? `
          <!-- Payment Declined Actions -->
          <div style="background-color: #f0fff4; border-radius: 8px; padding: 25px; margin-bottom: 30px; border-left: 4px solid #48bb78;">
            <h3 style="color: #2d3748; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
              🔧 How to Resolve This
            </h3>
            
            <div style="margin-bottom: 15px;">
              <h4 style="color: #2d3748; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">
                1. Contact Your Bank
              </h4>
              <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
                Check with your bank or card issuer to understand why the payment was declined.
              </p>
            </div>
            
            <div style="margin-bottom: 15px;">
              <h4 style="color: #2d3748; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">
                2. Update Payment Method
              </h4>
              <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
                Try using a different payment method or update your current card information.
              </p>
            </div>
            
            <div>
              <h4 style="color: #2d3748; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">
                3. Retry Payment
              </h4>
              <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
                Once resolved, you can retry your subscription in the app settings.
              </p>
            </div>
          </div>
          ` : ''}

          ${supportMessage ? `
          <!-- Custom Support Message -->
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h4 style="color: #2d3748; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">
              📞 Additional Information
            </h4>
            <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
              ${supportMessage}
            </p>
          </div>
          ` : ''}

          <!-- Support -->
          <div style="background-color: #f7fafc; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h4 style="color: #2d3748; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">
              Need Help?
            </h4>
            <p style="color: #4a5568; margin: 0; font-size: 14px; line-height: 1.5;">
              If you have any questions or need assistance, please contact our support team at 
              <a href="mailto:<EMAIL>" style="color: #4299e1; text-decoration: none;"><EMAIL></a>
            </p>
          </div>

        </div>

        <!-- Footer -->
        <div style="background-color: #2d3748; padding: 30px; text-align: center;">
          <p style="color: #a0aec0; margin: 0 0 15px 0; font-size: 14px;">
            We're here to help resolve any issues
          </p>
          
          <div style="margin-bottom: 20px;">
            <a href="https://www.aiplanmytrip.com" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Website</a>
            <a href="https://www.aiplanmytrip.com/help" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Help Center</a>
            <a href="https://www.aiplanmytrip.com/contact" style="color: #63b3ed; text-decoration: none; margin: 0 15px; font-size: 14px;">Contact</a>
          </div>
          
          <p style="color: #718096; margin: 0; font-size: 12px;">
            © 2024 Trip Itinerary Planner. All rights reserved.
          </p>
          
          <p style="color: #718096; margin: 10px 0 0 0; font-size: 11px;">
            <a href="https://www.aiplanmytrip.com/privacy" style="color: #718096; text-decoration: none;">Privacy Policy</a> | 
            <a href="https://www.aiplanmytrip.com/terms" style="color: #718096; text-decoration: none;">Terms of Service</a>
          </p>
        </div>

      </div>
    </body>
    </html>
  `;
}

export function generatePaymentIssuesText(data: PaymentIssuesData): string {
  const { userName, planName, issueType, issueDate, amount, transactionId, nextAction, supportMessage } = data;

  const getIssueTitle = () => {
    switch (issueType) {
      case 'grace_period_expired':
        return 'Grace Period Expired';
      case 'payment_declined':
        return 'Payment Declined';
      case 'subscription_cancelled':
        return 'Subscription Cancelled';
      case 'refund_processed':
        return 'Refund Processed';
      default:
        return 'Payment Issue';
    }
  };

  const getIssueDescription = () => {
    switch (issueType) {
      case 'grace_period_expired':
        return 'Your subscription grace period has expired and your account has been downgraded to the free plan.';
      case 'payment_declined':
        return 'Your payment was declined by your bank or card issuer.';
      case 'subscription_cancelled':
        return 'Your subscription has been cancelled and you\'ve been moved to the free plan.';
      case 'refund_processed':
        return 'A refund has been processed for your recent purchase.';
      default:
        return 'We encountered an issue with your payment or subscription.';
    }
  };

  return `
Trip Itinerary Planner - ${getIssueTitle()}

Hello ${userName},

${getIssueDescription()}

Issue Details:
- Plan: ${planName} Subscription
- Issue Date: ${issueDate}
${amount ? `- Amount: $${amount.toFixed(2)} USD` : ''}
${transactionId ? `- Transaction ID: ${transactionId}` : ''}

${issueType === 'grace_period_expired' ? `
📱 You're Now on the Free Plan:
You can still use Trip Itinerary Planner with 5 days per month on the free plan.
To restore your full subscription benefits, please update your payment method and resubscribe.
` : ''}

${issueType === 'refund_processed' ? `
💰 Refund Information:
Your refund has been processed and should appear in your account within 3-5 business days.
The refund amount will be credited to the original payment method used for the purchase.
` : ''}

${nextAction ? `
🔧 What You Can Do:
${nextAction}
` : ''}

${issueType === 'payment_declined' ? `
🔧 How to Resolve This:

1. Contact Your Bank
   Check with your bank or card issuer to understand why the payment was declined.

2. Update Payment Method
   Try using a different payment method or update your current card information.

3. Retry Payment
   Once resolved, you can retry your subscription in the app settings.
` : ''}

${supportMessage ? `
📞 Additional Information:
${supportMessage}
` : ''}

Need Help?
If you have any questions or need assistance, please contact our support <NAME_EMAIL>

We're here to help resolve any issues.

Visit us at: https://www.aiplanmytrip.com
Help Center: https://www.aiplanmytrip.com/help
Contact Us: https://www.aiplanmytrip.com/contact

© 2024 Trip Itinerary Planner. All rights reserved.

Privacy Policy: https://www.aiplanmytrip.com/privacy
Terms of Service: https://www.aiplanmytrip.com/terms
  `;
}
