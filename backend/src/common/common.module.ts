import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { RetryService } from './retry.service';
import { EmailService } from './email.service';
import { PdfReceiptService } from './pdf-receipt.service';
import { PaymentEmailService } from './payment-email.service';
import { ConfigModule } from '../config/config.module';
import { PaymentReceipt, PaymentReceiptSchema } from '../payments/schemas/payment-receipt.schema';
import { User, UserSchema } from '../users/schemas/user.schema';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: PaymentReceipt.name, schema: PaymentReceiptSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  providers: [RetryService, EmailService, PdfReceiptService, PaymentEmailService],
  exports: [RetryService, EmailService, PdfReceiptService, PaymentEmailService],
})
export class CommonModule { }
