import { Injectable, Logger } from '@nestjs/common';
import * as PDFDocument from 'pdfkit';
import { PaymentReceipt } from '../payments/schemas/payment-receipt.schema';

export interface ReceiptData {
  receipt: PaymentReceipt;
  userEmail: string;
  userName: string;
  companyInfo: {
    name: string;
    address: string;
    email: string;
    website: string;
  };
}

@Injectable()
export class PdfReceiptService {
  private readonly logger = new Logger(PdfReceiptService.name);

  /**
   * Generate a PDF receipt for a payment
   * @param receiptData Receipt and user data
   * @returns Buffer containing the PDF
   */
  async generateReceipt(receiptData: ReceiptData): Promise<Buffer> {
    try {
      this.logger.log(`Generating PDF receipt for transaction: ${receiptData.receipt.transactionId}`);

      return new Promise((resolve, reject) => {
        const doc = new PDFDocument({ margin: 50 });
        const buffers: Buffer[] = [];

        doc.on('data', (chunk) => buffers.push(chunk));
        doc.on('end', () => {
          const pdfBuffer = Buffer.concat(buffers);
          resolve(pdfBuffer);
        });
        doc.on('error', reject);

        // Generate the PDF content
        this.generatePdfContent(doc, receiptData);
        doc.end();
      });
    } catch (error) {
      this.logger.error(`Error generating PDF receipt: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate the PDF content
   */
  private generatePdfContent(doc: PDFKit.PDFDocument, receiptData: ReceiptData): void {
    const { receipt, userEmail, userName, companyInfo } = receiptData;

    // Header
    this.addHeader(doc, companyInfo);

    // Receipt title
    doc.fontSize(24)
      .fillColor('#2563eb')
      .text('Payment Receipt', 50, 150, { align: 'center' });

    // Receipt details
    this.addReceiptDetails(doc, receipt, userEmail, userName);

    // Payment information
    this.addPaymentInformation(doc, receipt);

    // Footer
    this.addFooter(doc, companyInfo);
  }

  /**
   * Add header with company information
   */
  private addHeader(doc: PDFKit.PDFDocument, companyInfo: any): void {
    doc.fontSize(20)
      .fillColor('#1f2937')
      .text(companyInfo.name, 50, 50);

    doc.fontSize(10)
      .fillColor('#6b7280')
      .text(companyInfo.address, 50, 80)
      .text(`Email: ${companyInfo.email}`, 50, 95)
      .text(`Website: ${companyInfo.website}`, 50, 110);

    // Add a line separator
    doc.moveTo(50, 130)
      .lineTo(550, 130)
      .strokeColor('#e5e7eb')
      .stroke();
  }

  /**
   * Add receipt details section
   */
  private addReceiptDetails(doc: PDFKit.PDFDocument, receipt: PaymentReceipt, userEmail: string, userName: string): void {
    const startY = 200;

    doc.fontSize(14)
      .fillColor('#1f2937')
      .text('Receipt Details', 50, startY);

    const detailsY = startY + 25;
    doc.fontSize(10)
      .fillColor('#374151');

    // Left column
    doc.text('Receipt ID:', 50, detailsY)
      .text('Transaction ID:', 50, detailsY + 15)
      .text('Date:', 50, detailsY + 30)
      .text('Customer:', 50, detailsY + 45)
      .text('Email:', 50, detailsY + 60);

    // Right column
    doc.text((receipt as any)._id.toString(), 150, detailsY)
      .text(receipt.transactionId, 150, detailsY + 15)
      .text(this.formatDate(receipt.purchaseDate || (receipt as any).createdAt), 150, detailsY + 30)
      .text(userName, 150, detailsY + 45)
      .text(userEmail, 150, detailsY + 60);
  }

  /**
   * Add payment information section
   */
  private addPaymentInformation(doc: PDFKit.PDFDocument, receipt: PaymentReceipt): void {
    const startY = 320;

    doc.fontSize(14)
      .fillColor('#1f2937')
      .text('Payment Information', 50, startY);

    const paymentY = startY + 25;
    doc.fontSize(10)
      .fillColor('#374151');

    // Left column
    doc.text('Product:', 50, paymentY)
      .text('Package Type:', 50, paymentY + 15)
      .text('Platform:', 50, paymentY + 30)
      .text('Status:', 50, paymentY + 45);

    // Right column
    const productDescription = this.getProductDescription(receipt);
    doc.text(productDescription, 150, paymentY)
      .text(receipt.packageType || 'N/A', 150, paymentY + 15)
      .text(receipt.platform.toUpperCase(), 150, paymentY + 30)
      .text(receipt.status.toUpperCase(), 150, paymentY + 45);

    // Amount section (highlighted)
    const amountY = paymentY + 80;
    doc.rect(50, amountY - 10, 500, 40)
      .fillColor('#f3f4f6')
      .fill();

    doc.fontSize(12)
      .fillColor('#1f2937')
      .text('Total Amount:', 60, amountY)
      .fontSize(16)
      .fillColor('#059669')
      .text(`$${receipt.amount.toFixed(2)} USD`, 400, amountY, { align: 'right' });
  }

  /**
   * Add footer with additional information
   */
  private addFooter(doc: PDFKit.PDFDocument, companyInfo: any): void {
    const footerY = 650;

    // Add a line separator
    doc.moveTo(50, footerY - 20)
      .lineTo(550, footerY - 20)
      .strokeColor('#e5e7eb')
      .stroke();

    doc.fontSize(8)
      .fillColor('#6b7280')
      .text('Thank you for your purchase!', 50, footerY)
      .text('This is an automatically generated receipt.', 50, footerY + 12)
      .text(`For support, please contact us at ${companyInfo.email}`, 50, footerY + 24)
      .text(`Generated on ${this.formatDate(new Date())}`, 50, footerY + 36);
  }

  /**
   * Get product description based on receipt data
   */
  private getProductDescription(receipt: PaymentReceipt): string {
    if (receipt.packageType === 'subscription') {
      const planName = this.getPlanName(receipt.productId);
      return `${planName} Subscription (${receipt.daysPerMonth} days/month)`;
    } else if (receipt.packageType === 'day_refill') {
      return `Day Refill Package (${receipt.days} days)`;
    } else {
      return receipt.productId;
    }
  }

  /**
   * Get plan name from product ID
   */
  private getPlanName(productId: string): string {
    if (productId.includes('free')) return 'Free';
    if (productId.includes('pro')) return 'Pro';
    if (productId.includes('premium')) return 'Premium';
    return 'Unknown Plan';
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(new Date(date));
  }
}
