import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EmailService } from './email.service';
import { PdfReceiptService, ReceiptData } from './pdf-receipt.service';
import { PaymentReceipt } from '../payments/schemas/payment-receipt.schema';
import { User } from '../users/schemas/user.schema';
import {
  generatePurchaseCompletionHtml,
  generatePurchaseCompletionText,
  PurchaseCompletionData
} from './email-templates/purchase-completion.helper';
import {
  generateRenewalSuccessHtml,
  generateRenewalSuccessText,
  RenewalSuccessData
} from './email-templates/renewal-success.helper';
import {
  generateRenewalFailureHtml,
  generateRenewalFailureText,
  RenewalFailureData
} from './email-templates/renewal-failure.helper';
import {
  generatePaymentIssuesHtml,
  generatePaymentIssuesText,
  PaymentIssuesData
} from './email-templates/payment-issues.helper';

@Injectable()
export class PaymentEmailService {
  private readonly logger = new Logger(PaymentEmailService.name);

  private readonly companyInfo = {
    name: 'Trip Itinerary Planner',
    address: 'AI Travel Solutions Inc.\n123 Innovation Drive\nSan Francisco, CA 94105',
    email: '<EMAIL>',
    website: 'https://www.aiplanmytrip.com'
  };

  constructor(
    private readonly emailService: EmailService,
    private readonly pdfReceiptService: PdfReceiptService,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(PaymentReceipt.name) private paymentReceiptModel: Model<PaymentReceipt>,
  ) { }

  /**
   * Send purchase completion email with receipt
   */
  async sendPurchaseCompletionEmail(
    userId: string,
    paymentReceiptId: string,
    additionalData?: {
      nextBillingDate?: string;
    }
  ): Promise<void> {
    try {
      this.logger.log(`Sending purchase completion email for user ${userId}, receipt ${paymentReceiptId}`);

      const { user, receipt } = await this.getUserAndReceipt(userId, paymentReceiptId);

      // Generate PDF receipt
      const pdfBuffer = await this.generateReceiptPdf(user, receipt);

      // Prepare email data
      const emailData: PurchaseCompletionData = {
        userName: user.name,
        productName: this.getProductName(receipt),
        amount: receipt.amount,
        transactionId: receipt.transactionId,
        purchaseDate: this.formatDate(receipt.purchaseDate || (receipt as any).createdAt),
        planDetails: receipt.packageType === 'subscription' ? {
          daysPerMonth: receipt.daysPerMonth || 0,
          nextBillingDate: additionalData?.nextBillingDate
        } : undefined,
        dayRefillDetails: receipt.packageType === 'day_refill' ? {
          days: receipt.days || 0
        } : undefined
      };

      // Generate email content
      const html = generatePurchaseCompletionHtml(emailData);
      const text = generatePurchaseCompletionText(emailData);

      // Send email with PDF attachment
      await this.emailService.sendEmail({
        to: user.email,
        subject: '🎉 Purchase Confirmed - Trip Itinerary Planner',
        html,
        text,
        attachments: [{
          filename: `receipt-${receipt.transactionId}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }]
      });

      this.logger.log(`Purchase completion email sent successfully to ${user.email}`);
    } catch (error) {
      this.logger.error(`Error sending purchase completion email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send renewal success email with receipt
   */
  async sendRenewalSuccessEmail(
    userId: string,
    paymentReceiptId: string,
    additionalData: {
      nextBillingDate: string;
    }
  ): Promise<void> {
    try {
      this.logger.log(`Sending renewal success email for user ${userId}, receipt ${paymentReceiptId}`);

      const { user, receipt } = await this.getUserAndReceipt(userId, paymentReceiptId);

      // Generate PDF receipt
      const pdfBuffer = await this.generateReceiptPdf(user, receipt);

      // Prepare email data
      const emailData: RenewalSuccessData = {
        userName: user.name,
        planName: this.getPlanName(receipt.productId),
        amount: receipt.amount,
        daysPerMonth: receipt.daysPerMonth || 0,
        renewalDate: this.formatDate(receipt.purchaseDate || (receipt as any).createdAt),
        nextBillingDate: additionalData.nextBillingDate,
        transactionId: receipt.transactionId
      };

      // Generate email content
      const html = generateRenewalSuccessHtml(emailData);
      const text = generateRenewalSuccessText(emailData);

      // Send email with PDF attachment
      await this.emailService.sendEmail({
        to: user.email,
        subject: '✅ Subscription Renewed - Trip Itinerary Planner',
        html,
        text,
        attachments: [{
          filename: `renewal-receipt-${receipt.transactionId}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf'
        }]
      });

      this.logger.log(`Renewal success email sent successfully to ${user.email}`);
    } catch (error) {
      this.logger.error(`Error sending renewal success email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send renewal failure email
   */
  async sendRenewalFailureEmail(
    userId: string,
    failureData: {
      planName: string;
      amount: number;
      failureDate: string;
      retryDate?: string;
      gracePeriodEnd?: string;
      reason?: string;
    }
  ): Promise<void> {
    try {
      this.logger.log(`Sending renewal failure email for user ${userId}`);

      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      // Prepare email data
      const emailData: RenewalFailureData = {
        userName: user.name,
        planName: failureData.planName,
        amount: failureData.amount,
        failureDate: failureData.failureDate,
        retryDate: failureData.retryDate,
        gracePeriodEnd: failureData.gracePeriodEnd,
        reason: failureData.reason
      };

      // Generate email content
      const html = generateRenewalFailureHtml(emailData);
      const text = generateRenewalFailureText(emailData);

      // Send email
      await this.emailService.sendEmail({
        to: user.email,
        subject: '⚠️ Subscription Renewal Failed - Trip Itinerary Planner',
        html,
        text
      });

      this.logger.log(`Renewal failure email sent successfully to ${user.email}`);
    } catch (error) {
      this.logger.error(`Error sending renewal failure email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send payment issues email
   */
  async sendPaymentIssuesEmail(
    userId: string,
    issueData: {
      planName: string;
      issueType: 'grace_period_expired' | 'payment_declined' | 'subscription_cancelled' | 'refund_processed';
      issueDate: string;
      amount?: number;
      transactionId?: string;
      nextAction?: string;
      supportMessage?: string;
    }
  ): Promise<void> {
    try {
      this.logger.log(`Sending payment issues email for user ${userId}, issue type: ${issueData.issueType}`);

      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      // Prepare email data
      const emailData: PaymentIssuesData = {
        userName: user.name,
        planName: issueData.planName,
        issueType: issueData.issueType,
        issueDate: issueData.issueDate,
        amount: issueData.amount,
        transactionId: issueData.transactionId,
        nextAction: issueData.nextAction,
        supportMessage: issueData.supportMessage
      };

      // Generate email content
      const html = generatePaymentIssuesHtml(emailData);
      const text = generatePaymentIssuesText(emailData);

      // Get subject based on issue type
      const subject = this.getIssueEmailSubject(issueData.issueType);

      // Send email
      await this.emailService.sendEmail({
        to: user.email,
        subject,
        html,
        text
      });

      this.logger.log(`Payment issues email sent successfully to ${user.email}`);
    } catch (error) {
      this.logger.error(`Error sending payment issues email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get user and receipt data
   */
  private async getUserAndReceipt(userId: string, paymentReceiptId: string): Promise<{
    user: User;
    receipt: PaymentReceipt;
  }> {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    const receipt = await this.paymentReceiptModel.findById(paymentReceiptId);
    if (!receipt) {
      throw new Error(`Payment receipt not found: ${paymentReceiptId}`);
    }

    return { user, receipt };
  }

  /**
   * Generate PDF receipt
   */
  private async generateReceiptPdf(user: User, receipt: PaymentReceipt): Promise<Buffer> {
    const receiptData: ReceiptData = {
      receipt,
      userEmail: user.email,
      userName: user.name,
      companyInfo: this.companyInfo
    };

    return this.pdfReceiptService.generateReceipt(receiptData);
  }

  /**
   * Get product name from receipt
   */
  private getProductName(receipt: PaymentReceipt): string {
    if (receipt.packageType === 'subscription') {
      const planName = this.getPlanName(receipt.productId);
      return `${planName} Subscription`;
    } else if (receipt.packageType === 'day_refill') {
      return `Day Refill Package (${receipt.days} days)`;
    } else {
      return receipt.productId;
    }
  }

  /**
   * Get plan name from product ID
   */
  private getPlanName(productId: string): string {
    if (productId.includes('free')) return 'Free';
    if (productId.includes('pro')) return 'Pro';
    if (productId.includes('premium')) return 'Premium';
    return 'Unknown Plan';
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(new Date(date));
  }

  /**
   * Get email subject based on issue type
   */
  private getIssueEmailSubject(issueType: string): string {
    switch (issueType) {
      case 'grace_period_expired':
        return '⏰ Grace Period Expired - Trip Itinerary Planner';
      case 'payment_declined':
        return '❌ Payment Declined - Trip Itinerary Planner';
      case 'subscription_cancelled':
        return '🚫 Subscription Cancelled - Trip Itinerary Planner';
      case 'refund_processed':
        return '💰 Refund Processed - Trip Itinerary Planner';
      default:
        return '⚠️ Payment Issue - Trip Itinerary Planner';
    }
  }
}
