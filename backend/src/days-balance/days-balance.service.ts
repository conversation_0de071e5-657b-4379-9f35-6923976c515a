import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ClientSession, Model } from 'mongoose';
import { ConfigService } from '../config/config.service';
import { DatabaseErrorHandler } from '../common/database-error.util';
import { DayBalance, SubscriptionPlan, SubscriptionStatus } from '../users/schemas/token-balance.schema';
import {
  DayTransaction,
  TransactionSource,
  TransactionType,
} from '../users/schemas/token-transaction.schema';
import { User } from '../users/schemas/user.schema';
import { ProductConfig, ProductConfigService } from '../payments/services/product-config.service';

@Injectable()
export class DaysBalanceService {
  private readonly logger = new Logger(DaysBalanceService.name);
  private transactionsSupported: boolean = true;
  private readonly subscriptionCycle: number = 60_000 * 5; // in milliseconds

  constructor(
    @InjectModel(DayBalance.name)
    private dayBalanceModel: Model<DayBalance>,
    @InjectModel(DayTransaction.name)
    private dayTransactionModel: Model<DayTransaction>,
    @InjectModel(User.name) private userModel: Model<User>,
    private readonly configService: ConfigService,
    private readonly productConfigService: ProductConfigService,
  ) {
    // Check if transactions are supported
    this.checkTransactionsSupport();
  }

  /**
   * Get plan information from product config service
   */
  private getPlanInfo(plan: SubscriptionPlan): { daysPerMonth: number; displayName: string } {
    const planId = plan; // Enum values already match product IDs (free, pro, premium)
    const products = this.productConfigService.getAllProducts();
    const planProduct = products.find(p => p.id === planId && p.type === 'subscription');

    if (!planProduct) {
      // Fallback to default values if product not found
      const defaults = {
        [SubscriptionPlan.BASIC]: { daysPerMonth: 5, displayName: 'Free' },
        [SubscriptionPlan.RECOMMENDED]: { daysPerMonth: 50, displayName: 'Pro' },
        [SubscriptionPlan.PREMIUM]: { daysPerMonth: 100, displayName: 'Premium' },
      };
      return defaults[plan] || { daysPerMonth: 5, displayName: 'Free' };
    }

    return {
      daysPerMonth: planProduct.daysPerMonth || 5,
      displayName: planProduct.name
    };
  }

  /**
   * Get day pack size from product config service
   * Returns the smallest day pack size available
   */
  private getDayPack(numberDays: number): ProductConfig {
    const dayPackProducts = this.productConfigService
      .getDayRefillPackages()
      .filter(p => p.days && p.days >= numberDays);

    return dayPackProducts[0]
  }

  /**
   * Check if MongoDB transactions are supported
   */
  private async checkTransactionsSupport(): Promise<void> {
    try {
      const session = await this.dayBalanceModel.db.startSession();

      try {
        session.startTransaction();
        await this.dayBalanceModel.findOne({}).session(session);
        await session.commitTransaction();
        this.transactionsSupported = true;
        this.logger.log('MongoDB transactions are supported');
      } catch (txError) {
        this.transactionsSupported = false;
        this.logger.warn(
          `MongoDB transactions are not supported: ${txError.message}`,
        );
      } finally {
        session.endSession();
      }
    } catch (error) {
      this.transactionsSupported = false;
      this.logger.warn(
        'MongoDB transactions are not supported. Running in non-transactional mode.',
      );
    }

    // Force non-transactional mode for standalone MongoDB servers
    this.transactionsSupported = false;
    this.logger.warn(
      'Forcing non-transactional mode for compatibility with standalone MongoDB server',
    );
  }

  /**
   * Get a user's day balance
   */
  async getBalance(userId: string): Promise<DayBalance | null> {
    return await DatabaseErrorHandler.safeExecuteWithNull(
      async () => {
        const balance = await this.dayBalanceModel.findOne({
          userId: userId?.toString(),
        });
        return balance as DayBalance;
      },
      'get user balance',
      { userId }
    );
  }

  /**
   * Get total available days (subscription + pack days)
   */
  async getTotalAvailableDays(userId: string): Promise<number> {
    const balance = await this.getBalance(userId);
    if (!balance) return 0;
    return balance.subscriptionDays + balance.packDays;
  }

  /**
   * Initialize a user's day balance with basic plan
   */
  async initializeUserBalance(
    userId: string,
    plan: SubscriptionPlan = SubscriptionPlan.BASIC,
  ): Promise<DayBalance> {
    // Check if user exists
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const planInfo = this.getPlanInfo(plan);
    const now = new Date();
    const subscriptionEndDate = new Date(now.getTime() + this.subscriptionCycle);

    try {
      const balance = await this.dayBalanceModel.findOneAndUpdate(
        { userId },
        {
          $setOnInsert: {
            userId,
            subscriptionDays: planInfo.daysPerMonth,
            packDays: 0,
            totalDaysUsed: 0,
            totalDaysAdded: planInfo.daysPerMonth,
            currentPlan: plan,
            subscriptionStatus: SubscriptionStatus.ACTIVE,
            subscriptionStartDate: now,
            subscriptionEndDate,
            nextBillingDate: subscriptionEndDate,
            lastResetDate: now,
          },
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true,
        },
      );

      // Update user with reference to day balance if not already set
      if (!user.dayBalanceId) {
        user.dayBalanceId = balance._id as any;
        await user.save();
      }

      // Create initial subscription transaction
      const existingTransaction = await this.dayTransactionModel.findOne({
        userId,
        source: TransactionSource.INITIAL_GRANT,
      });

      if (!existingTransaction) {
        await this.createTransaction({
          userId,
          type: TransactionType.CREDIT,
          amount: planInfo.daysPerMonth,
          balanceAfter: planInfo.daysPerMonth,
          source: TransactionSource.INITIAL_GRANT,
          description: `Welcome ${planInfo.displayName} plan`,
        });
      }

      return balance;
    } catch (error) {
      if (error.code === 11000) {
        this.logger.warn(
          `Duplicate day balance detected for user ${userId}, returning existing balance`,
        );
        const existingBalance = await this.dayBalanceModel.findOne({ userId });
        if (existingBalance) {
          return existingBalance;
        }
      }

      this.logger.error(
        `Error initializing day balance for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Deduct days from a user's balance
   */
  async deductDays(
    userId: string,
    amount: number = 1,
    source: TransactionSource = TransactionSource.TRIP_GENERATION,
    description: string = 'Day used for trip generation',
    tripId?: string,
    metadata?: Record<string, any>,
  ): Promise<DayBalance> {
    if (amount <= 0) {
      throw new BadRequestException('Day amount must be greater than 0');
    }

    const balance = await this.getBalance(userId);
    if (!balance) {
      throw new NotFoundException(`Day balance not found for user ${userId}`);
    }

    const totalAvailable = balance.subscriptionDays + balance.packDays;
    if (totalAvailable < amount) {
      throw new BadRequestException(
        `Insufficient days. Available: ${totalAvailable}, Required: ${amount}`,
      );
    }

    // Deduct from subscription days first, then pack days
    let newSubscriptionDays = balance.subscriptionDays;
    let newPackDays = balance.packDays;

    if (balance.subscriptionDays >= amount) {
      newSubscriptionDays -= amount;
    } else {
      const remainingToDeduct = amount - balance.subscriptionDays;
      newSubscriptionDays = 0;
      newPackDays -= remainingToDeduct;
    }

    const newTotalDays = newSubscriptionDays + newPackDays;

    if (this.transactionsSupported) {
      return this.deductDaysWithTransaction(
        userId,
        amount,
        source,
        description,
        tripId,
        metadata,
        balance,
        newSubscriptionDays,
        newPackDays,
        newTotalDays,
      );
    } else {
      return this.deductDaysWithoutTransaction(
        userId,
        amount,
        source,
        description,
        tripId,
        metadata,
        balance,
        newSubscriptionDays,
        newPackDays,
        newTotalDays,
      );
    }
  }

  /**
   * Check if user has sufficient days
   */
  async hasSufficientDays(userId: string, amount: number = 1): Promise<boolean> {
    try {
      const totalDays = await this.getTotalAvailableDays(userId);
      return totalDays >= amount;
    } catch (error) {
      this.logger.error(
        `Error checking day balance for user ${userId}: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Purchase day packs
   */
  async purchaseDayPack(
    userId: string,
    quantity: number,
    metadata?: Record<string, any>,
  ): Promise<DayBalance> {
    if (quantity <= 0) {
      throw new BadRequestException('Quantity must be greater than 0');
    }

    const balance = await this.getBalance(userId);
    if (!balance) {
      throw new NotFoundException(`Day balance not found for user ${userId}`);
    }

    const dayPackSize = this.getDayPack(quantity)
    const daysToAdd = dayPackSize.days || quantity;
    const newPackDays = balance.packDays + daysToAdd;
    const newTotalDays = balance.subscriptionDays + newPackDays;

    // Calculate expiration date (end of current subscription cycle)
    const expiresAt = balance.subscriptionEndDate || new Date();

    const updatedBalance = await this.dayBalanceModel.findByIdAndUpdate(
      balance._id,
      {
        $inc: {
          packDays: daysToAdd,
          totalDaysAdded: daysToAdd,
        },
      },
      { new: true },
    );

    // Create transaction record
    await this.createTransaction({
      userId,
      type: TransactionType.CREDIT,
      amount: daysToAdd,
      balanceAfter: newTotalDays,
      source: TransactionSource.DAY_PACK_PURCHASE,
      description: `Purchased ${quantity} day pack(s) (${daysToAdd} days)`,
      metadata: { ...metadata, quantity, daysPerPack: quantity },
      expiresAt,
    });

    this.logger.log(
      `User ${userId} purchased ${quantity} day pack(s) (${daysToAdd} days). New balance: ${(updatedBalance?.subscriptionDays || 0) + (updatedBalance?.packDays || 0)}`,
    );

    return updatedBalance as DayBalance;
  }

  /**
   * Deduct days with transaction support
   */
  private async deductDaysWithTransaction(
    userId: string,
    amount: number,
    source: TransactionSource,
    description: string,
    tripId: string | undefined,
    metadata: Record<string, any> | undefined,
    balance: DayBalance,
    newSubscriptionDays: number,
    newPackDays: number,
    newTotalDays: number,
  ): Promise<DayBalance> {
    let session: ClientSession | null = null;
    try {
      session = await this.dayBalanceModel.db.startSession();
      session.startTransaction();

      const updatedBalance = await this.dayBalanceModel.findByIdAndUpdate(
        balance._id,
        {
          $set: {
            subscriptionDays: newSubscriptionDays,
            packDays: newPackDays,
          },
          $inc: {
            totalDaysUsed: amount,
          },
        },
        { new: true, session },
      );

      await this.createTransaction(
        {
          userId,
          type: TransactionType.DEBIT,
          amount,
          balanceAfter: newTotalDays,
          source,
          tripId,
          description,
          metadata,
        },
        session,
      );

      await session.commitTransaction();
      this.logger.log(
        `Used ${amount} day(s) from user ${userId}. New balance: ${newTotalDays}`,
      );

      return updatedBalance as DayBalance;
    } catch (error) {
      if (session) {
        await session.abortTransaction();
      }
      this.logger.error(
        `Failed to deduct days with transaction: ${error.message}`,
      );
      throw error;
    } finally {
      if (session) {
        session.endSession();
      }
    }
  }

  /**
   * Deduct days without transaction support
   */
  private async deductDaysWithoutTransaction(
    userId: string,
    amount: number,
    source: TransactionSource,
    description: string,
    tripId: string | undefined,
    metadata: Record<string, any> | undefined,
    balance: DayBalance,
    newSubscriptionDays: number,
    newPackDays: number,
    newTotalDays: number,
  ): Promise<DayBalance> {
    try {
      const updatedBalance = await this.dayBalanceModel.findByIdAndUpdate(
        balance._id,
        {
          $set: {
            subscriptionDays: newSubscriptionDays,
            packDays: newPackDays,
          },
          $inc: {
            totalDaysUsed: amount,
          },
        },
        { new: true },
      );

      await this.createTransaction({
        userId,
        type: TransactionType.DEBIT,
        amount,
        balanceAfter: newTotalDays,
        source,
        tripId,
        description,
        metadata,
      });

      this.logger.log(
        `Used ${amount} day(s) from user ${userId} (non-transactional). New balance: ${newTotalDays}`,
      );

      return updatedBalance as DayBalance;
    } catch (error) {
      this.logger.error(
        `Failed to deduct days without transaction: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get transaction history
   */
  async getTransactionHistory(
    userId: string,
    limit: number = 10,
    page: number = 1,
  ): Promise<{
    data: DayTransaction[];
    pagination: {
      totalItems: number;
      totalPages: number;
      currentPage: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }> {
    const skip = (page - 1) * limit;

    const [totalItems, data] = await Promise.all([
      this.dayTransactionModel.countDocuments({ userId }).exec(),
      this.dayTransactionModel
        .find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .exec(),
    ]);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      pagination: {
        totalItems,
        totalPages,
        currentPage: page,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Reset subscription days for monthly billing cycle
   */
  async resetSubscriptionDays(userId: string, renewalDate?: Date, expiryDate?: Date): Promise<DayBalance> {
    const balance = await this.getBalance(userId);
    if (!balance) {
      throw new NotFoundException(`Day balance not found for user ${userId}`);
    }

    let currentPlan = balance.currentPlan;
    let planInfo = this.getPlanInfo(currentPlan);
    const currentPurchaseDate = renewalDate || balance.nextBillingDate || new Date();
    const nextResetDate = expiryDate || new Date(currentPurchaseDate.getTime() + this.subscriptionCycle);

    // Check if there's a pending plan change that should take effect
    let updateFields: any = {
      lastResetDate: currentPurchaseDate,
      subscriptionStartDate: currentPurchaseDate,
      subscriptionEndDate: nextResetDate,
      nextBillingDate: nextResetDate,
    };

    if (balance.pendingPlanChange && balance.pendingPlanChange.effectiveDate <= currentPurchaseDate) {
      // Apply the pending plan change
      currentPlan = balance.pendingPlanChange.newPlan;
      planInfo = this.getPlanInfo(currentPlan);

      updateFields.currentPlan = currentPlan;
      updateFields.$unset = { pendingPlanChange: 1 }; // Remove the pending change

      this.logger.log(
        `Applying pending plan change for user ${userId} from ${balance.pendingPlanChange.oldPlan} to ${currentPlan}`,
      );
    }

    // Set subscription days to the (possibly new) plan allowance
    updateFields.subscriptionDays = planInfo.daysPerMonth;

    // Remove expired pack days
    await this.removeExpiredPackDays(userId);

    // Reset subscription days to plan allowance
    const updatedBalance = await this.dayBalanceModel.findByIdAndUpdate(
      balance._id,
      {
        $set: updateFields,
        $inc: {
          totalDaysAdded: planInfo.daysPerMonth,
        },
      },
      { new: true },
    );

    // Create transaction record for subscription renewal
    const description = balance.pendingPlanChange && balance.pendingPlanChange.effectiveDate <= currentPurchaseDate
      ? `Plan changed to ${planInfo.displayName} and renewed (${planInfo.daysPerMonth} days)`
      : `Monthly ${planInfo.displayName} plan renewal`;

    await this.createTransaction({
      userId,
      type: TransactionType.CREDIT,
      amount: planInfo.daysPerMonth,
      balanceAfter: updatedBalance!.subscriptionDays + updatedBalance!.packDays,
      source: TransactionSource.SUBSCRIPTION_RENEWAL,
      description,
      metadata: {
        plan: currentPlan,
        resetDate: currentPurchaseDate,
        planChangeApplied: balance.pendingPlanChange && balance.pendingPlanChange.effectiveDate <= currentPurchaseDate,
        previousPlan: balance.pendingPlanChange?.oldPlan
      },
    });

    this.logger.log(
      `Reset subscription days for user ${userId}. New subscription days: ${planInfo.daysPerMonth}${currentPlan !== balance.currentPlan ? ` (plan changed to ${currentPlan})` : ''}`,
    );

    return updatedBalance as DayBalance;
  }

  /**
   * Remove expired pack days
   */
  async removeExpiredPackDays(userId: string): Promise<void> {
    const now = new Date();

    // Find expired pack transactions
    const expiredPacks = await this.dayTransactionModel.find({
      userId,
      source: TransactionSource.DAY_PACK_PURCHASE,
      expiresAt: { $lte: now },
    });

    if (expiredPacks.length === 0) {
      return;
    }

    const totalExpiredDays = expiredPacks.reduce((sum, pack) => sum + pack.amount, 0);

    // Update balance to remove expired pack days
    const balance = await this.getBalance(userId);
    if (balance && balance.packDays > 0) {
      const daysToRemove = Math.min(totalExpiredDays, balance.packDays);

      await this.dayBalanceModel.findByIdAndUpdate(
        balance._id,
        {
          $inc: {
            packDays: -daysToRemove,
          },
        },
      );

      // Create transaction record for expired days
      await this.createTransaction({
        userId,
        type: TransactionType.DEBIT,
        amount: daysToRemove,
        balanceAfter: balance.subscriptionDays + (balance.packDays - daysToRemove),
        source: TransactionSource.DAY_PACK_PURCHASE,
        description: `Expired day pack days removed`,
        metadata: { expiredDays: daysToRemove, expiredPacks: expiredPacks.length },
      });

      this.logger.log(
        `Removed ${daysToRemove} expired pack days for user ${userId}`,
      );
    }
  }

  /**
   * Check if user needs subscription reset
   */
  async checkSubscriptionReset(userId: string): Promise<boolean> {
    const balance = await this.getBalance(userId);
    if (!balance || !balance.nextBillingDate) {
      return false;
    }

    const now = new Date();
    return now >= balance.nextBillingDate;
  }

  /**
   * Update subscription plan
   */
  async updateSubscriptionPlan(
    userId: string,
    newPlan: SubscriptionPlan,
    effectiveDate?: Date,
    expiryDate?: Date,
  ): Promise<DayBalance> {
    const balance = await this.getBalance(userId);
    if (!balance) {
      throw new NotFoundException(`Day balance not found for user ${userId}`);
    }

    const oldPlanInfo = this.getPlanInfo(balance.currentPlan);
    const newPlanInfo = this.getPlanInfo(newPlan);
    const purchaseDate = effectiveDate || new Date();

    // Check if this is an upgrade or downgrade
    const isUpgrade = newPlanInfo.daysPerMonth > oldPlanInfo.daysPerMonth;
    const isDowngrade = newPlanInfo.daysPerMonth < oldPlanInfo.daysPerMonth;

    let updatedBalance: DayBalance;

    if (isUpgrade) {
      // For upgrades, immediately add the difference in days and I do not wish to deduct old plan days
      const newDays = newPlanInfo.daysPerMonth;
      const nextBillingDate = expiryDate || new Date(purchaseDate.getTime() + this.subscriptionCycle);

      updatedBalance = await this.dayBalanceModel.findByIdAndUpdate(
        balance._id,
        {
          $set: {
            currentPlan: newPlan,
            subscriptionStartDate: purchaseDate,
            subscriptionEndDate: nextBillingDate,
            nextBillingDate,
          },
          $inc: {
            subscriptionDays: newDays,
            totalDaysAdded: newDays,
          },
        },
        { new: true },
      ) as DayBalance;

      // Create transaction record for upgrade
      await this.createTransaction({
        userId,
        type: TransactionType.CREDIT,
        amount: newDays,
        balanceAfter: updatedBalance.subscriptionDays + updatedBalance.packDays,
        source: TransactionSource.SUBSCRIPTION_RENEWAL,
        description: `Plan upgraded from ${oldPlanInfo.displayName} to ${newPlanInfo.displayName}`,
        metadata: {
          oldPlan: balance.currentPlan,
          newPlan,
          daysDifference: newDays,
          changeDate: purchaseDate,
          changeType: 'upgrade'
        },
      });
    } else if (isDowngrade) {
      // Check if we're at the due date (billing cycle end) or if it's an immediate downgrade
      const now = new Date();
      const nextBillingDate = balance.nextBillingDate || new Date();
      const isAtDueDate = now >= nextBillingDate || (expiryDate && now >= expiryDate);

      if (isAtDueDate || effectiveDate && effectiveDate <= now) {
        // Apply downgrade immediately if we're at the due date or effective date has passed
        const newDays = newPlanInfo.daysPerMonth;
        const nextBillingDate = expiryDate || new Date(purchaseDate.getTime() + this.subscriptionCycle);

        updatedBalance = await this.dayBalanceModel.findByIdAndUpdate(
          balance._id,
          {
            $set: {
              currentPlan: newPlan,
              subscriptionStartDate: purchaseDate,
              subscriptionEndDate: nextBillingDate,
              nextBillingDate,
              subscriptionDays: newDays, // Set to new plan's allowance
            },
            $inc: {
              totalDaysAdded: newDays,
            },
            $unset: {
              pendingPlanChange: 1, // Remove any pending plan change
            },
          },
          { new: true },
        ) as DayBalance;

        // Create transaction record for immediate downgrade
        await this.createTransaction({
          userId,
          type: TransactionType.CREDIT,
          amount: newDays,
          balanceAfter: updatedBalance.subscriptionDays + updatedBalance.packDays,
          source: TransactionSource.SUBSCRIPTION_RENEWAL,
          description: `Plan downgraded from ${oldPlanInfo.displayName} to ${newPlanInfo.displayName} (${newDays} days)`,
          metadata: {
            oldPlan: balance.currentPlan,
            newPlan,
            changeDate: purchaseDate,
            changeType: 'downgrade_immediate',
            effectiveDate: purchaseDate,
            newDays: newDays,
            reason: isAtDueDate ? 'due_date' : 'effective_date_passed'
          },
        });
      } else {
        // Schedule downgrade for next billing cycle (existing behavior)
        updatedBalance = await this.dayBalanceModel.findByIdAndUpdate(
          balance._id,
          {
            $set: {
              // Keep the current plan active until next billing cycle
              // Store the pending plan change to be applied at next reset
              pendingPlanChange: {
                newPlan,
                oldPlan: balance.currentPlan,
                changeDate: purchaseDate,
                effectiveDate: balance.nextBillingDate || new Date(),
              }
            },
          },
          { new: true },
        ) as DayBalance;

        // Create transaction record for scheduled downgrade
        await this.createTransaction({
          userId,
          type: TransactionType.CREDIT, // No actual change, just logging
          amount: 0,
          balanceAfter: updatedBalance.subscriptionDays + updatedBalance.packDays,
          source: TransactionSource.SUBSCRIPTION_RENEWAL,
          description: `Plan change scheduled from ${oldPlanInfo.displayName} to ${newPlanInfo.displayName} (effective next billing cycle)`,
          metadata: {
            oldPlan: balance.currentPlan,
            newPlan,
            changeDate: purchaseDate,
            changeType: 'downgrade_scheduled',
            effectiveDate: balance.nextBillingDate || new Date(),
            preservedDays: balance.subscriptionDays
          },
        });
      }
    } else {
      // Same plan, no changes needed
      updatedBalance = balance;
      // remove pending plan change if it exists
      if (balance.pendingPlanChange) {
        await this.dayBalanceModel.findByIdAndUpdate(
          balance._id,
          {
            $unset: { pendingPlanChange: 1 },
          },
        );
      }
    }

    // Determine the action taken for logging
    let actionTaken = 'Updated';
    let effectiveInfo = '';

    if (isDowngrade) {
      const now = new Date();
      const nextBillingDate = balance.nextBillingDate || new Date();
      const isAtDueDate = now >= nextBillingDate || (expiryDate && now >= expiryDate);

      if (isAtDueDate || effectiveDate && effectiveDate <= now) {
        actionTaken = 'Downgraded';
        effectiveInfo = ' (effective immediately - at due date)';
      } else {
        actionTaken = 'Scheduled downgrade';
        effectiveInfo = ' (effective next billing cycle)';
      }
    } else if (isUpgrade) {
      actionTaken = 'Upgraded';
      effectiveInfo = ' (effective immediately)';
    }

    this.logger.log(
      `${actionTaken} subscription plan for user ${userId} from ${balance.currentPlan} to ${newPlan}${effectiveInfo}`,
    );

    return updatedBalance;
  }

  /**
   * Create a day transaction record
   */
  private async createTransaction(
    transactionData: {
      userId: string;
      type: TransactionType;
      amount: number;
      balanceAfter: number;
      source: TransactionSource;
      tripId?: string;
      description?: string;
      metadata?: Record<string, any>;
      expiresAt?: Date;
    },
    session?: any,
  ): Promise<DayTransaction> {
    const transaction = new this.dayTransactionModel(transactionData);

    if (session) {
      return transaction.save({ session });
    }

    return transaction.save();
  }
}
