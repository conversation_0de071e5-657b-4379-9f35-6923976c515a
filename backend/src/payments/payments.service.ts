/* eslint-disable */

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DaysBalanceService } from '../days-balance/days-balance.service';
import { PaymentEmailService } from '../common/payment-email.service';

import {
  RestorePurchaseResult,
  RestorePurchasesDto,
} from './dto/restore-purchases.dto';
import { ValidatePurchaseDto } from './dto/validate-purchase.dto';
import {
  PaymentPlatform,
  PaymentReceipt,
  PaymentStatus,
} from './schemas/payment-receipt.schema';
import { ApplePaymentService } from './services/apple-payment.service';
import { GooglePaymentService } from './services/google-payment.service';
import { ProductConfigService } from './services/product-config.service';

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);

  constructor(
    @InjectModel(PaymentReceipt.name)
    private paymentReceiptModel: Model<PaymentReceipt>,
    private readonly daysBalanceService: DaysBalanceService,
    private readonly applePaymentService: ApplePaymentService,
    private readonly googlePaymentService: GooglePaymentService,
    private readonly productConfigService: ProductConfigService,
    private readonly paymentEmailService: PaymentEmailService
  ) { }

  /**
   * Validate a purchase and add credits to the user's account
   * @param userId User ID
   * @param validatePurchaseDto Purchase validation data
   * @returns Validation result
   */
  async validatePurchase(
    userId: string,
    validatePurchaseDto: ValidatePurchaseDto,
  ) {
    try {
      const {
        receipt,
        productId,
        packageId,
        platform,
        packageType,
        days,
        daysPerMonth,
        originalTransactionId,
        transactionId: incomingTransactionId,
        transactionDate
      } = validatePurchaseDto;

      // Get the appropriate payment service based on the platform
      const paymentService = this.getPaymentServiceForPlatform(platform);

      // Validate the receipt with the payment service
      const validationResult = await paymentService.validateReceipt(
        receipt,
        productId,
        userId,
        {
          packageId, packageType, days, daysPerMonth, originalTransactionId,
          transactionId: incomingTransactionId, transactionDate
        },
      );

      // If the receipt is not valid, return an error
      if (!validationResult.isValid) {
        this.logger.error(
          `Invalid receipt for user ${userId}, product ${productId}: ${validationResult.error}`,
        );
        throw new BadRequestException(
          `Invalid receipt: ${validationResult.error}`,
        );
      }

      // Check if this transaction has already been processed
      const transactionId = validationResult.transactionId;
      const isProcessed =
        await paymentService.isTransactionProcessed(transactionId);

      if (isProcessed) {
        this.logger.warn(
          `Transaction ${transactionId} has already been processed for user ${userId}`,
        );
        return {
          success: true,
          message: 'Transaction already processed',
          transactionId,
        };
      }

      const productInfo = this.productConfigService.getProductByStoreId(
        productId,
        platform
      );

      // Create a payment receipt record
      const paymentReceipt = new this.paymentReceiptModel({
        userId,
        productId,
        packageId,
        transactionId,
        originalTransactionId: validationResult.originalTransactionId,
        receipt,
        platform,
        status: PaymentStatus.COMPLETED,
        amount: productInfo?.price.usd,
        packageType,
        days,
        daysPerMonth,
        purchaseDate: validationResult.purchaseDate,
        expiryDate: validationResult.expiryDate,
        validationResponse: validationResult.validationResponse,
      });

      await paymentReceipt.save();

      // Check expiry date for subscriptions
      if (packageType === 'subscription' && validationResult.expiryDate) {
        const now = new Date();
        if (now > validationResult.expiryDate) {
          this.logger.error(
            `Expired subscription purchase for user ${userId}, product ${productId}`,
          );
          return {
            success: false,
            message: 'Expired subscription purchase',
            transactionId,
          };
        }
      }

      // Handle different package types
      let resultMessage = '';
      if (packageType === 'subscription') {
        // Get current plan to determine if this is an upgrade or downgrade
        const currentBalance = await this.daysBalanceService.getBalance(userId);
        const currentPlan = currentBalance?.currentPlan || 'free';

        // Define plan hierarchy for upgrade/downgrade logic
        const planHierarchy = { free: 0, pro: 1, premium: 2 };
        const currentLevel = planHierarchy[currentPlan as keyof typeof planHierarchy] || 0;
        const newLevel = planHierarchy[packageId as keyof typeof planHierarchy] || 0;

        if (newLevel > currentLevel) {
          // Upgrade: Apply immediately
          await this.daysBalanceService.updateSubscriptionPlan(
            userId,
            packageId as any,
            validationResult.purchaseDate,
            validationResult.expiryDate,
          );
          resultMessage = `Successfully upgraded to ${packageId} subscription (${daysPerMonth} days/month) - Active immediately`;
        } else if (newLevel < currentLevel) {
          // Downgrade: Schedule for next billing cycle
          // For now, we'll apply immediately but in production this should be scheduled
          await this.daysBalanceService.updateSubscriptionPlan(
            userId,
            packageId as any,
            validationResult.purchaseDate,
            validationResult.expiryDate,
          );
          resultMessage = `Downgrade to ${packageId} subscription scheduled for next billing cycle`;
        } else {
          // Same level (shouldn't happen but handle gracefully)
          resultMessage = `Subscription plan ${packageId} confirmed`;
        }
      } else if (packageType === 'day_refill' && days) {
        await this.daysBalanceService.purchaseDayPack(
          userId,
          days,
          { transactionId, packageId, platform },
        );
        resultMessage = `Successfully added ${days} days to your account`;
      }

      this.logger.log(
        `Successfully processed ${packageType} purchase for user ${userId}, product ${productId}`,
      );

      // Send purchase completion email
      try {
        let nextBillingDate: string | undefined;
        if (packageType === 'subscription' && validationResult.expiryDate) {
          nextBillingDate = this.formatDate(validationResult.expiryDate);
        }

        await this.paymentEmailService.sendPurchaseCompletionEmail(
          userId,
          (paymentReceipt as any)._id.toString(),
          nextBillingDate ? { nextBillingDate } : undefined
        );
      } catch (error) {
        this.logger.error(`Error sending purchase completion email: ${error.message}`, error.stack);
        // Don't throw - email failure shouldn't break purchase processing
      }

      return {
        success: true,
        message: resultMessage,
        transactionId,
      };
    } catch (error) {
      this.logger.error(
        `Error validating purchase: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Restore previous purchases for a user
   * @param userId User ID
   * @param restorePurchasesDto Restore purchases data
   * @returns Restore result
   */
  async restorePurchases(
    userId: string,
    restorePurchasesDto: RestorePurchasesDto,
  ) {
    try {
      const { purchases, platform } = restorePurchasesDto;

      // Get the appropriate payment service based on the platform
      const paymentService = this.getPaymentServiceForPlatform(platform);

      let restoredCount = 0;
      const results: RestorePurchaseResult[] = [];

      // Process each purchase
      for (const purchase of purchases) {
        try {
          // Validate the receipt
          const validationResult = await paymentService.validateReceipt(
            purchase.receipt,
            purchase.productId,
            userId,
          );

          // If the receipt is not valid, skip it
          if (!validationResult.isValid) {
            results.push({
              transactionId: purchase.transactionId,
              success: false,
              message: `Invalid receipt: ${validationResult.error}`,
            });
            continue;
          }

          // Check if this transaction has already been processed
          const transactionId =
            validationResult.transactionId || purchase.transactionId;
          const isProcessed =
            await paymentService.isTransactionProcessed(transactionId);

          if (isProcessed) {
            results.push({
              transactionId,
              success: true,
              message: 'Transaction already processed',
            });
            continue;
          }

          // Find the package ID and credits based on the product ID
          // In a real implementation, you would look this up in your database
          const packageInfo = this.productConfigService.getProductByStoreId(
            purchase.productId,
            platform
          );

          if (!packageInfo) {
            results.push({
              transactionId,
              success: false,
              message: `Unknown product ID: ${purchase.productId}`,
            });
            continue;
          }

          // Create a payment receipt record
          const paymentReceipt = new this.paymentReceiptModel({
            userId,
            productId: purchase.productId,
            packageId: packageInfo.id,
            transactionId,
            originalTransactionId: validationResult.originalTransactionId,
            receipt: purchase.receipt,
            platform,
            status: PaymentStatus.COMPLETED,
            amount: 0, // We don't know the actual amount paid
            packageType: packageInfo.type,
            days: packageInfo.type === 'consumable' ? packageInfo.days : undefined,
            daysPerMonth: packageInfo.type === 'subscription' ? packageInfo.daysPerMonth : undefined,
            purchaseDate:
              validationResult.purchaseDate ||
              new Date(purchase.transactionDate),
            expiryDate: validationResult.expiryDate,
            isRestored: true,
            validationResponse: validationResult.validationResponse,
          });

          await paymentReceipt.save();

          // Add day packs to the user's account (convert credits to day packs)
          if (packageInfo.type === 'consumable' && packageInfo.days) {
            await this.daysBalanceService.purchaseDayPack(
              userId,
              packageInfo.days,
              {
                transactionId,
                packageId: packageInfo.id,
                isRestored: true,
                platform,
              },
            );
          }

          restoredCount++;
          results.push({
            transactionId,
            success: true,
            message: `Restored purchase and added ${packageInfo.days} days`,
          });
        } catch (error) {
          this.logger.error(
            `Error restoring purchase ${purchase.transactionId}: ${error.message}`,
            error.stack,
          );
          results.push({
            transactionId: purchase.transactionId,
            success: false,
            message: `Error: ${error.message}`,
          });
        }
      }

      return {
        success: true,
        restoredCount,
        results,
      };
    } catch (error) {
      this.logger.error(
        `Error restoring purchases: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get the appropriate payment service for a platform
   * @param platform Payment platform
   * @returns Payment service
   */
  private getPaymentServiceForPlatform(platform: PaymentPlatform) {
    switch (platform) {
      case PaymentPlatform.IOS:
        return this.applePaymentService;
      case PaymentPlatform.ANDROID:
        return this.googlePaymentService;
      default:
        throw new BadRequestException(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Get user's payment history
   * @param userId User ID
   * @returns Payment history
   */
  async getUserPaymentHistory(userId: string) {
    try {
      const receipts = await this.paymentReceiptModel
        .find({ userId })
        .sort({ createdAt: -1 })
        .lean();

      return receipts.map(receipt => ({
        id: receipt._id,
        date: receipt.purchaseDate,
        description: this.getReceiptDescription(receipt),
        amount: receipt.amount,
        status: receipt.status.toLowerCase(),
        transactionId: receipt.transactionId,
        platform: receipt.platform,
        packageType: receipt.packageType,
      }));
    } catch (error) {
      this.logger.error(`Error getting payment history for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate human-readable description for receipt
   */
  private getReceiptDescription(receipt: any): string {
    if (receipt.packageType === 'subscription') {
      const planName = receipt.packageId?.charAt(0).toUpperCase() + receipt.packageId?.slice(1) || 'Unknown';
      return `${planName} Plan Subscription`;
    } else if (receipt.packageType === 'day_refill') {
      return `${receipt.days || 'Unknown'} Days Pack`;
    } else {
      return 'In-App Purchase';
    }
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(new Date(date));
  }
}
