import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '../../config/config.service';
import { DaysBalanceService } from '../../days-balance/days-balance.service';
import { PaymentEmailService } from '../../common/payment-email.service';
import { WebhookVerificationService } from './webhook-verification.service';
import { PaymentReceipt, PaymentPlatform, PaymentStatus } from '../schemas/payment-receipt.schema';
import {
  GoogleWebhookDto,
  GoogleDeveloperNotification,
  GoogleSubscriptionNotificationType,
  GoogleOneTimeProductNotificationType
} from '../dto/google-webhook.dto';

@Injectable()
export class GoogleWebhookService {
  private readonly logger = new Logger(GoogleWebhookService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly daysBalanceService: DaysBalanceService,
    private readonly webhookVerificationService: WebhookVerificationService,
    private readonly paymentEmailService: PaymentEmailService,
    @InjectModel(PaymentReceipt.name)
    private paymentReceiptModel: Model<PaymentReceipt>,
  ) { }

  /**
   * Process Google Play Real-time Developer Notification
   */
  async processWebhook(webhookDto: GoogleWebhookDto): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log('Processing Google Play webhook notification');

      // Verify the webhook message
      const verificationResult = this.webhookVerificationService.verifyGoogleWebhook(webhookDto.message);
      if (!verificationResult.isValid) {
        throw new BadRequestException(`Webhook verification failed: ${verificationResult.error}`);
      }

      // Decode the base64 message data
      const messageData = Buffer.from(webhookDto.message.data, 'base64').toString('utf-8');
      const notification: GoogleDeveloperNotification = JSON.parse(messageData);

      this.logger.log(`Received Google Play notification for package: ${notification.packageName}`);

      // Process based on notification type
      await this.handleNotification(notification);

      return {
        success: true,
        message: 'Webhook processed successfully'
      };
    } catch (error) {
      this.logger.error(`Error processing Google Play webhook: ${error.message}`, error.stack);
      throw new BadRequestException(`Webhook processing failed: ${error.message}`);
    }
  }

  /**
   * Handle different notification types
   */
  private async handleNotification(notification: GoogleDeveloperNotification): Promise<void> {
    if (notification.subscriptionNotification) {
      await this.handleSubscriptionNotification(notification.subscriptionNotification, notification.packageName);
    } else if (notification.oneTimeProductNotification) {
      await this.handleOneTimeProductNotification(notification.oneTimeProductNotification, notification.packageName);
    } else if (notification.testNotification) {
      this.logger.log('Received test notification from Google Play');
    } else {
      this.logger.warn('Unknown Google Play notification type');
    }
  }

  /**
   * Handle subscription notifications
   */
  private async handleSubscriptionNotification(
    subNotification: any,
    packageName: string
  ): Promise<void> {
    const { notificationType, purchaseToken, subscriptionId } = subNotification;

    this.logger.log(`Processing subscription notification type: ${notificationType}`);

    const userId = await this.findUserByPurchaseToken(purchaseToken);
    if (!userId) {
      this.logger.warn(`User not found for purchase token: ${purchaseToken}`);
      return;
    }

    switch (notificationType) {
      case GoogleSubscriptionNotificationType.SUBSCRIPTION_PURCHASED:
        await this.handleSubscriptionPurchased(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_RENEWED:
        await this.handleSubscriptionRenewed(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_CANCELED:
        await this.handleSubscriptionCanceled(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_EXPIRED:
        await this.handleSubscriptionExpired(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_RECOVERED:
        await this.handleSubscriptionRecovered(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_ON_HOLD:
        await this.handleSubscriptionOnHold(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_IN_GRACE_PERIOD:
        await this.handleSubscriptionInGracePeriod(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_RESTARTED:
        await this.handleSubscriptionRestarted(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_PAUSED:
        await this.handleSubscriptionPaused(userId, subscriptionId, purchaseToken);
        break;

      case GoogleSubscriptionNotificationType.SUBSCRIPTION_REVOKED:
        await this.handleSubscriptionRevoked(userId, subscriptionId, purchaseToken);
        break;

      default:
        this.logger.warn(`Unhandled subscription notification type: ${notificationType}`);
    }
  }

  /**
   * Handle one-time product notifications
   */
  private async handleOneTimeProductNotification(
    productNotification: any,
    packageName: string
  ): Promise<void> {
    const { notificationType, purchaseToken, sku } = productNotification;

    this.logger.log(`Processing one-time product notification type: ${notificationType}`);

    const userId = await this.findUserByPurchaseToken(purchaseToken);
    if (!userId) {
      this.logger.warn(`User not found for purchase token: ${purchaseToken}`);
      return;
    }

    switch (notificationType) {
      case GoogleOneTimeProductNotificationType.ONE_TIME_PRODUCT_PURCHASED:
        await this.handleOneTimeProductPurchased(userId, sku, purchaseToken);
        break;

      case GoogleOneTimeProductNotificationType.ONE_TIME_PRODUCT_CANCELED:
        await this.handleOneTimeProductCanceled(userId, sku, purchaseToken);
        break;

      default:
        this.logger.warn(`Unhandled one-time product notification type: ${notificationType}`);
    }
  }

  /**
   * Handle subscription purchased
   */
  private async handleSubscriptionPurchased(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing subscription purchase for user ${userId}`);

    const planId = this.mapProductIdToPlan(subscriptionId);
    if (planId) {
      await this.daysBalanceService.updateSubscriptionPlan(userId, planId as any);
    }

    // Send purchase completion email
    try {
      const receipt = await this.paymentReceiptModel.findOne({
        userId,
        productId: subscriptionId,
        platform: PaymentPlatform.ANDROID
      }).sort({ createdAt: -1 });

      if (receipt) {
        // Calculate next billing date (30 days from now for subscriptions)
        const nextBillingDate = new Date();
        nextBillingDate.setDate(nextBillingDate.getDate() + 30);

        await this.paymentEmailService.sendPurchaseCompletionEmail(
          userId,
          (receipt as any)._id.toString(),
          {
            nextBillingDate: this.formatDate(nextBillingDate)
          }
        );
      }
    } catch (error) {
      this.logger.error(`Error sending purchase completion email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Handle subscription renewed
   */
  private async handleSubscriptionRenewed(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing subscription renewal for user ${userId}`);

    // Reset subscription days
    await this.daysBalanceService.resetSubscriptionDays(userId);

    // Send renewal success email
    try {
      const receipt = await this.paymentReceiptModel.findOne({
        userId,
        productId: subscriptionId,
        platform: PaymentPlatform.ANDROID
      }).sort({ createdAt: -1 });

      if (receipt) {
        // Calculate next billing date (30 days from now)
        const nextBillingDate = new Date();
        nextBillingDate.setDate(nextBillingDate.getDate() + 30);

        await this.paymentEmailService.sendRenewalSuccessEmail(
          userId,
          (receipt as any)._id.toString(),
          {
            nextBillingDate: this.formatDate(nextBillingDate)
          }
        );
      }
    } catch (error) {
      this.logger.error(`Error sending renewal success email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Handle subscription canceled
   */
  private async handleSubscriptionCanceled(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing subscription cancellation for user ${userId}`);

    // Mark subscription as canceled but don't immediately downgrade
    // Let it expire naturally at the end of the billing period
    // You might want to update a cancellation flag in your database
  }

  /**
   * Handle subscription expired
   */
  private async handleSubscriptionExpired(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing subscription expiration for user ${userId}`);

    // Downgrade to free plan
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle subscription recovered
   */
  private async handleSubscriptionRecovered(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing subscription recovery for user ${userId}`);

    // Reactivate subscription
    const planId = this.mapProductIdToPlan(subscriptionId);
    if (planId) {
      await this.daysBalanceService.updateSubscriptionPlan(userId, planId as any);
    }
  }

  /**
   * Handle subscription on hold
   */
  private async handleSubscriptionOnHold(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Subscription on hold for user ${userId}`);

    // You might want to pause the subscription or mark it as on hold
    // Implementation depends on your business logic
  }

  /**
   * Handle subscription in grace period
   */
  private async handleSubscriptionInGracePeriod(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Subscription in grace period for user ${userId}`);

    // Keep subscription active during grace period
    // You might want to notify the user about payment issues
  }

  /**
   * Handle subscription restarted
   */
  private async handleSubscriptionRestarted(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing subscription restart for user ${userId}`);

    // Reactivate subscription
    const planId = this.mapProductIdToPlan(subscriptionId);
    if (planId) {
      await this.daysBalanceService.updateSubscriptionPlan(userId, planId as any);
    }
  }

  /**
   * Handle subscription paused
   */
  private async handleSubscriptionPaused(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Subscription paused for user ${userId}`);

    // Handle subscription pause
    // Implementation depends on your business logic
  }

  /**
   * Handle subscription revoked
   */
  private async handleSubscriptionRevoked(userId: string, subscriptionId: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing subscription revocation for user ${userId}`);

    // Immediately downgrade to free plan
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle one-time product purchased
   */
  private async handleOneTimeProductPurchased(userId: string, sku: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing one-time product purchase for user ${userId}`);

    // Handle day refill purchase
    if (sku === 'com.itrip.days.refill_10') {
      const dayPacks = 1; // 10 days = 1 pack
      await this.daysBalanceService.purchaseDayPack(userId, dayPacks, {
        transactionId: purchaseToken,
        packageId: 'refill_10',
        platform: PaymentPlatform.ANDROID
      });
    }
  }

  /**
   * Handle one-time product canceled
   */
  private async handleOneTimeProductCanceled(userId: string, sku: string, purchaseToken: string): Promise<void> {
    this.logger.log(`Processing one-time product cancellation for user ${userId}`);

    // Handle refund logic if needed
    // Implementation depends on your business logic
  }

  /**
   * Find user by purchase token
   */
  private async findUserByPurchaseToken(purchaseToken: string): Promise<string | null> {
    const receipt = await this.paymentReceiptModel.findOne({
      receipt: purchaseToken,
      platform: PaymentPlatform.ANDROID
    });

    return receipt ? receipt.userId : null;
  }

  /**
   * Map Google Play product ID to internal plan ID
   */
  private mapProductIdToPlan(productId: string): string | null {
    const mapping: Record<string, string> = {
      'com.itrip.subscription.free': 'free',
      'com.itrip.subscription.pro': 'pro',
      'com.itrip.subscription.premium': 'premium'
    };

    return mapping[productId] || null;
  }

  /**
   * Get plan name from product ID
   */
  private getPlanNameFromProductId(productId: string): string {
    if (productId.includes('free')) return 'Free';
    if (productId.includes('pro')) return 'Pro';
    if (productId.includes('premium')) return 'Premium';
    return 'Unknown Plan';
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(new Date(date));
  }
}
