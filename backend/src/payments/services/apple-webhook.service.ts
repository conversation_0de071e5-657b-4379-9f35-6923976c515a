import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  decodeTransaction,
  decodeRenewalInfo,
  JWSTransactionDecodedPayload,
  JWSRenewalInfoDecodedPayload
} from 'app-store-server-api';
import { Model } from 'mongoose';
import { DaysBalanceService } from '../../days-balance/days-balance.service';
import { PaymentEmailService } from '../../common/payment-email.service';
import {
  AppleNotificationPayloadDto,
  AppleNotificationType,
  AppleWebhookDto
} from '../dto/apple-webhook.dto';
import { PaymentPlatform, PaymentReceipt } from '../schemas/payment-receipt.schema';
import { WebhookVerificationService } from './webhook-verification.service';
import { ProductConfigService } from './product-config.service';

@Injectable()
export class AppleWebhookService {
  private readonly logger = new Logger(AppleWebhookService.name);

  constructor(
    private readonly daysBalanceService: DaysBalanceService,
    private readonly webhookVerificationService: WebhookVerificationService,
    private readonly paymentEmailService: PaymentEmailService,
    @InjectModel(PaymentReceipt.name)
    private paymentReceiptModel: Model<PaymentReceipt>,
    private readonly productConfigService: ProductConfigService,
  ) { }

  /**
   * Process Apple App Store Server Notification
   */
  async processWebhook(webhookDto: AppleWebhookDto): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log('Processing Apple webhook notification', webhookDto.signedPayload);

      // Verify and decode the signed payload
      const payload = await this.verifyAndDecodePayload(webhookDto.signedPayload);

      this.logger.log(`Received Apple notification: ${payload.notificationType}`);

      // Process based on notification type
      await this.handleNotification(payload);

      return {
        success: true,
        message: 'Webhook processed successfully'
      };
    } catch (error) {
      this.logger.error(`Error processing Apple webhook: ${error.message}`, error.stack);
      throw new BadRequestException(`Webhook processing failed: ${error.message}`);
    }
  }

  /**
   * Verify JWT signature and decode payload
   */
  private async verifyAndDecodePayload(signedPayload: string): Promise<AppleNotificationPayloadDto> {
    try {
      // Use proper signature verification
      const verificationResult = await this.webhookVerificationService.verifyAppleWebhook(signedPayload);

      if (!verificationResult.isValid) {
        throw new Error(`Webhook verification failed: ${verificationResult.error}`);
      }

      return verificationResult.payload as AppleNotificationPayloadDto;
    } catch (error) {
      this.logger.error(`JWT verification failed: ${error.message}`);
      throw new Error('Invalid webhook signature');
    }
  }

  /**
   * Handle different notification types
   */
  private async handleNotification(payload: AppleNotificationPayloadDto): Promise<void> {
    const { notificationType, data } = payload;

    switch (notificationType) {
      case AppleNotificationType.SUBSCRIBED:
        await this.handleSubscribed(data);
        break;

      case AppleNotificationType.DID_RENEW:
        await this.handleRenewal(data);
        break;

      case AppleNotificationType.DID_FAIL_TO_RENEW:
        await this.handleRenewalFailure(data);
        break;

      case AppleNotificationType.EXPIRED:
        await this.handleExpiration(data);
        break;

      case AppleNotificationType.DID_CHANGE_RENEWAL_STATUS:
        await this.handleRenewalStatusChange(data);
        break;

      case AppleNotificationType.REFUND:
        await this.handleRefund(data);
        break;

      case AppleNotificationType.REVOKE:
        await this.handleRevocation(data);
        break;

      case AppleNotificationType.GRACE_PERIOD_EXPIRED:
        await this.handleGracePeriodExpired(data);
        break;

      case AppleNotificationType.TEST:
        this.logger.log('Received test notification from Apple');
        break;

      default:
        this.logger.warn(`Unhandled Apple notification type: ${notificationType}`);
    }
  }

  /**
   * Handle new subscription
   */
  private async handleSubscribed(data: any): Promise<void> {
    const transactionInfo = await this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);
    if (!userId) {
      this.logger.warn(`User not found for transaction: ${transactionInfo.originalTransactionId}`);
      return;
    }

    this.logger.log(`Processing subscription for user ${userId}`);

    // Update subscription status
    const planId = this.mapProductIdToPlan(transactionInfo.productId);
    if (planId) {
      await this.daysBalanceService.updateSubscriptionPlan(userId, planId as any);
    }

    // Send purchase completion email
    try {
      const receipt = await this.paymentReceiptModel.findOne({
        originalTransactionId: transactionInfo.originalTransactionId,
        platform: PaymentPlatform.IOS
      });

      if (receipt) {
        // Calculate next billing date (30 days from now for subscriptions)
        const nextBillingDate = new Date();
        nextBillingDate.setDate(nextBillingDate.getDate() + 30);

        await this.paymentEmailService.sendPurchaseCompletionEmail(
          userId,
          (receipt as any)._id.toString(),
          {
            nextBillingDate: this.formatDate(nextBillingDate)
          }
        );
      }
    } catch (error) {
      this.logger.error(`Error sending purchase completion email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Handle subscription renewal
   */
  private async handleRenewal(data: any): Promise<void> {
    const transactionInfo = await this.extractTransactionInfo(data);
    const renewalInfo = await this.extractRenewalInfo(data);

    this.logger.log(`Processing renewal - Transaction ID: ${transactionInfo.transactionId}, Product: ${transactionInfo.productId}`);

    // Check if this transaction has already been processed
    const existingTransaction = await this.paymentReceiptModel.findOne({
      transactionId: transactionInfo.transactionId,
      platform: PaymentPlatform.IOS
    });

    if (existingTransaction) {
      this.logger.warn(`Transaction ${transactionInfo.transactionId} already processed, skipping`);
      return;
    }

    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      this.logger.warn(`User not found for renewal: ${transactionInfo.originalTransactionId}`);
      return;
    }

    this.logger.log(`Processing renewal for user ${userId}, Product: ${transactionInfo.productId}`);

    // Get the current plan from the transaction info
    const currentPlan = this.mapProductIdToPlan(transactionInfo.productId);
    if (!currentPlan) {
      this.logger.error(`Unknown product ID in renewal: ${transactionInfo.productId}`);
      return;
    }

    // Check for plan changes by comparing with the most recent receipt
    const planChanged = await this.handlePlanChange(userId, transactionInfo, currentPlan);

    // Get user's current balance to check if plan actually changed
    const userBalance = await this.daysBalanceService.getBalance(userId);
    const isActualPlanChange = userBalance && userBalance.currentPlan !== currentPlan;

    if (isActualPlanChange || planChanged) {
      // Handle plan change during renewal
      this.logger.log(`Plan change detected during renewal for user ${userId}: ${userBalance?.currentPlan} -> ${currentPlan}`);
      await this.daysBalanceService.updateSubscriptionPlan(
        userId,
        currentPlan as any,
        undefined, // effectiveDate - let it be immediate
        transactionInfo.expiresDate ? new Date(transactionInfo.expiresDate) : undefined
      );
    } else {
      // Regular renewal with same plan - reset subscription days
      this.logger.log(`Regular renewal for user ${userId} with plan ${currentPlan}`);
      const renewalDate = new Date(transactionInfo.purchaseDate);
      const expiryDate = transactionInfo.expiresDate ? new Date(transactionInfo.expiresDate) : undefined;
      await this.daysBalanceService.resetSubscriptionDays(userId, renewalDate, expiryDate);
    }

    // Create a new receipt record for this renewal transaction
    await this.createRenewalReceipt(transactionInfo, renewalInfo, userId, currentPlan);

    // Send renewal success email
    await this.sendRenewalSuccessEmail(userId, transactionInfo);
  }

  /**
   * Handle renewal failure
   */
  private async handleRenewalFailure(data: any): Promise<void> {
    const transactionInfo = await this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing renewal failure for user ${userId}`);

    // Send renewal failure email
    try {
      const planName = this.getPlanNameFromProductId(transactionInfo.productId);

      // Calculate retry date (3 days from now)
      const retryDate = new Date();
      retryDate.setDate(retryDate.getDate() + 3);

      // Calculate grace period end (7 days from now)
      const gracePeriodEnd = new Date();
      gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 7);

      await this.paymentEmailService.sendRenewalFailureEmail(userId, {
        planName,
        amount: 0, // We don't have the amount in the webhook data
        failureDate: this.formatDate(new Date()),
        retryDate: this.formatDate(retryDate),
        gracePeriodEnd: this.formatDate(gracePeriodEnd),
        reason: 'Payment method declined'
      });
    } catch (error) {
      this.logger.error(`Error sending renewal failure email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }

  }

  /**
   * Handle subscription expiration
   */
  private async handleExpiration(data: any): Promise<void> {
    const transactionInfo = await this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing expiration for user ${userId}`);

    // Downgrade to free plan
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle refund
   */
  private async handleRefund(data: any): Promise<void> {
    const transactionInfo = await this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing refund for user ${userId}`);

    // Handle refund logic - might need to deduct days or downgrade plan
    this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle revocation
   */
  private async handleRevocation(data: any): Promise<void> {
    const transactionInfo = await this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Processing revocation for user ${userId}`);

    // Handle subscription revocation
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);
  }

  /**
   * Handle renewal status change (cancellation/reactivation)
   */
  private async handleRenewalStatusChange(data: any): Promise<void> {
    const renewalInfo = await this.extractRenewalInfo(data);
    const transactionInfo = await this.extractTransactionInfo(data);

    this.logger.log(`Renewal status changed - Auto-renew: ${renewalInfo.autoRenewStatus}, Product: ${transactionInfo.productId}`);

    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);
    if (!userId) {
      this.logger.warn(`User not found for renewal status change: ${transactionInfo.originalTransactionId}`);
      return;
    }

    // Check if subscription was cancelled (autoRenewStatus = 0)
    if (renewalInfo.autoRenewStatus === 0) {
      this.logger.log(`Subscription cancelled for user ${userId}, will expire at: ${transactionInfo.expiresDate}`);

      // Send cancellation email
      try {
        const planName = this.getPlanNameFromProductId(transactionInfo.productId);
        const expiryDate = transactionInfo.expiresDate
          ? new Date(transactionInfo.expiresDate)
          : new Date();

        await this.paymentEmailService.sendPaymentIssuesEmail(userId, {
          planName,
          issueType: 'subscription_cancelled',
          issueDate: this.formatDate(new Date()),
          nextAction: `Your subscription will remain active until ${this.formatDate(expiryDate)}. You can reactivate it anytime in the app settings.`,
          supportMessage: 'After expiry, you can still use Trip Itinerary Planner with 5 days per month on the free plan.'
        });
      } catch (error) {
        this.logger.error(`Error sending cancellation email: ${error.message}`, error.stack);
      }
    } else if (renewalInfo.autoRenewStatus === 1) {
      this.logger.log(`Subscription reactivated for user ${userId}`);
      // Subscription was reactivated - no immediate action needed
      // The subscription will continue to renew normally
    }

    // Update renewal status in database if you track this
    // You might want to add a field to track cancellation status
  }

  /**
   * Handle grace period expiration
   */
  private async handleGracePeriodExpired(data: any): Promise<void> {
    const transactionInfo = await this.extractTransactionInfo(data);
    const userId = await this.findUserByTransactionId(transactionInfo.originalTransactionId);

    if (!userId) {
      return;
    }

    this.logger.log(`Grace period expired for user ${userId}`);

    // Downgrade to free plan after grace period
    await this.daysBalanceService.updateSubscriptionPlan(userId, 'free' as any);

    // Send grace period expired email
    try {
      const planName = this.getPlanNameFromProductId(transactionInfo.productId);

      await this.paymentEmailService.sendPaymentIssuesEmail(userId, {
        planName,
        issueType: 'grace_period_expired',
        issueDate: this.formatDate(new Date()),
        nextAction: 'Update your payment method in the app settings to resubscribe and restore your full plan benefits.',
        supportMessage: 'You can still use Trip Itinerary Planner with 5 days per month on the free plan.'
      });
    } catch (error) {
      this.logger.error(`Error sending grace period expired email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Extract transaction info from webhook data
   */
  private async extractTransactionInfo(data: any): Promise<JWSTransactionDecodedPayload> {
    debugger
    // Use app-store-server-api library to properly decode and verify signed transaction info
    if (data.signedTransactionInfo) {
      return await decodeTransaction(data.signedTransactionInfo);
    }
    // Fallback to direct data if not signed (shouldn't happen in production)
    return data.transactionInfo;
  }

  /**
   * Extract renewal info from webhook data
   */
  private async extractRenewalInfo(data: any): Promise<JWSRenewalInfoDecodedPayload> {
    // Use app-store-server-api library to properly decode and verify signed renewal info
    if (data.signedRenewalInfo) {
      return await decodeRenewalInfo(data.signedRenewalInfo);
    }

    // Fallback to direct data if not signed (shouldn't happen in production)
    return data.renewalInfo;
  }

  /**
   * Find user by original transaction ID
   */
  private async findUserByTransactionId(originalTransactionId: string): Promise<string | null> {
    const receipt = await this.paymentReceiptModel.findOne({
      originalTransactionId,
      platform: PaymentPlatform.IOS
    });

    return receipt ? receipt.userId : null;
  }

  /**
   * Create a receipt record for renewal transaction
   */
  private async createRenewalReceipt(
    transactionInfo: JWSTransactionDecodedPayload,
    renewalInfo: JWSRenewalInfoDecodedPayload,
    userId: string,
    planId: string
  ): Promise<void> {
    try {
      const product = this.productConfigService.getProductById(planId);
      if (!product) {
        this.logger.error(`Product not found for plan: ${planId}`);
        return;
      }

      const renewalReceipt = new this.paymentReceiptModel({
        userId,
        productId: transactionInfo.productId,
        packageId: planId,
        transactionId: transactionInfo.transactionId,
        originalTransactionId: transactionInfo.originalTransactionId,
        receipt: JSON.stringify(transactionInfo), // Store the full transaction info
        platform: PaymentPlatform.IOS,
        status: 'completed',
        amount: product.price.usd,
        packageType: 'subscription',
        daysPerMonth: product.daysPerMonth,
        purchaseDate: new Date(transactionInfo.purchaseDate),
        expiryDate: transactionInfo.expiresDate ? new Date(transactionInfo.expiresDate) : undefined,
        metadata: {
          renewalInfo: renewalInfo,
          environment: transactionInfo.environment,
          isRenewal: true
        }
      });

      await renewalReceipt.save();
      this.logger.log(`Created renewal receipt for transaction: ${transactionInfo.transactionId}`);
    } catch (error) {
      this.logger.error(`Error creating renewal receipt: ${error.message}`, error.stack);
      // Don't throw - receipt creation failure shouldn't break webhook processing
    }
  }

  /**
   * Handle plan changes during renewal
   * @returns true if a plan change was detected, false otherwise
   */
  private async handlePlanChange(
    userId: string,
    transactionInfo: JWSTransactionDecodedPayload,
    newPlanId: string
  ): Promise<boolean> {
    try {
      // Get the most recent receipt for this user to compare plans
      const lastReceipt = await this.paymentReceiptModel.findOne({
        userId,
        platform: PaymentPlatform.IOS,
        packageType: 'subscription'
      }).sort({ createdAt: -1 });

      if (lastReceipt && lastReceipt.packageId !== newPlanId) {
        const oldPlan = this.productConfigService.getProductById(lastReceipt.packageId);
        const newPlan = this.productConfigService.getProductById(newPlanId);

        if (oldPlan && newPlan) {
          this.logger.log(`Plan change detected for user ${userId}: ${oldPlan.name} -> ${newPlan.name}`);

          // Log the change for analytics/debugging
          this.logger.log(`Plan change details:`, {
            userId,
            oldPlan: oldPlan.name,
            newPlan: newPlan.name,
            oldDaysPerMonth: oldPlan.daysPerMonth,
            newDaysPerMonth: newPlan.daysPerMonth,
            transactionId: transactionInfo.transactionId,
            productId: transactionInfo.productId
          });

          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error(`Error handling plan change: ${error.message}`, error.stack);
      // Don't throw - plan change detection failure shouldn't break webhook processing
      return false;
    }
  }

  /**
   * Send renewal success email
   */
  private async sendRenewalSuccessEmail(
    userId: string,
    transactionInfo: JWSTransactionDecodedPayload
  ): Promise<void> {
    try {
      const receipt = await this.paymentReceiptModel.findOne({
        transactionId: transactionInfo.transactionId,
        platform: PaymentPlatform.IOS
      });

      if (receipt) {
        // Calculate next billing date from expiry date
        const nextBillingDate = transactionInfo.expiresDate
          ? new Date(transactionInfo.expiresDate)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // Fallback to 30 days from now

        await this.paymentEmailService.sendRenewalSuccessEmail(
          userId,
          (receipt as any)._id.toString(),
          {
            nextBillingDate: this.formatDate(nextBillingDate)
          }
        );
      }
    } catch (error) {
      this.logger.error(`Error sending renewal success email: ${error.message}`, error.stack);
      // Don't throw - email failure shouldn't break webhook processing
    }
  }

  /**
   * Map Apple product ID to internal plan ID
   */
  private mapProductIdToPlan(productId: string): string | null {
    const product = this.productConfigService.getProductByStoreId(productId, 'ios');
    return product ? product.id : null;
  }

  /**
   * Get plan name from product ID
   */
  private getPlanNameFromProductId(productId: string): string {
    if (productId.includes('free')) return 'Free';
    if (productId.includes('pro')) return 'Pro';
    if (productId.includes('premium')) return 'Premium';
    return 'Unknown Plan';
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(new Date(date));
  }
}
