import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  Request,
  Logger,
  BadRequestException,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { PaymentsService } from './payments.service';
import { ProductConfigService } from './services/product-config.service';
import { SubscriptionLifecycleService } from './services/subscription-lifecycle.service';
import { ValidatePurchaseDto } from './dto/validate-purchase.dto';
import { RestorePurchasesDto } from './dto/restore-purchases.dto';

// Define request type with user property
interface RequestWithUser {
  user: {
    sub: string;
    email: string;
    [key: string]: any;
  };
}

@ApiTags('payments')
@Controller('payments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PaymentsController {
  private readonly logger = new Logger(PaymentsController.name);

  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly productConfigService: ProductConfigService,
    private readonly subscriptionLifecycleService: SubscriptionLifecycleService,
  ) {
    console.log('Payments Controller initialized ----- ');
  }

  @Post('validate')
  @ApiOperation({
    summary: 'Validate a purchase and add credits to the user account',
  })
  @ApiResponse({ status: 200, description: 'Purchase validated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid purchase data' })
  async validatePurchase(
    @Request() req: RequestWithUser,
    @Body() validatePurchaseDto: ValidatePurchaseDto,
  ) {
    try {
      if (!req.user?.sub) {
        throw new BadRequestException('User authentication required');
      }

      if (!validatePurchaseDto) {
        throw new BadRequestException('Purchase data is required');
      }

      this.logger.log(`Validating purchase for user ${req.user.sub}`);
      this.logger.log(`Purchase data: ${JSON.stringify(validatePurchaseDto, null, 2)}`);
      return await this.paymentsService.validatePurchase(
        req.user.sub,
        validatePurchaseDto,
      );
    } catch (error) {
      this.logger.error(`Error validating purchase for user ${req.user?.sub}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to validate purchase');
    }
  }

  @Post('restore')
  @ApiOperation({ summary: 'Restore previous purchases' })
  @ApiResponse({ status: 200, description: 'Purchases restored successfully' })
  @ApiResponse({ status: 400, description: 'Invalid restore data' })
  async restorePurchases(
    @Request() req: RequestWithUser,
    @Body() restorePurchasesDto: RestorePurchasesDto,
  ) {
    try {
      if (!req.user?.sub) {
        throw new BadRequestException('User authentication required');
      }

      if (!restorePurchasesDto) {
        throw new BadRequestException('Restore data is required');
      }

      this.logger.log(`Restoring purchases for user ${req.user.sub}`);
      return await this.paymentsService.restorePurchases(
        req.user.sub,
        restorePurchasesDto,
      );
    } catch (error) {
      this.logger.error(`Error restoring purchases for user ${req.user?.sub}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to restore purchases');
    }
  }

  @Get('products')
  @ApiOperation({ summary: 'Get available products and pricing' })
  @ApiResponse({ status: 200, description: 'Products retrieved successfully' })
  @ApiQuery({ name: 'platform', enum: ['ios', 'android'], required: false })
  async getProducts(@Query('platform') platform?: 'ios' | 'android') {
    try {
      this.logger.log(`Getting products for platform: ${platform || 'all'}`);

      if (platform) {
        return {
          success: true,
          products: this.productConfigService.getProductsByPlatform(platform),
          productIds: this.productConfigService.getProductIds(platform),
        };
      }

      return {
        success: true,
        products: this.productConfigService.getAllProducts(),
        validation: this.productConfigService.validateProductConfig(),
      };
    } catch (error) {
      this.logger.error(`Error getting products: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get products');
    }
  }

  @Get('products/subscriptions')
  @ApiOperation({ summary: 'Get subscription plans' })
  @ApiResponse({ status: 200, description: 'Subscription plans retrieved successfully' })
  @ApiQuery({ name: 'platform', enum: ['ios', 'android'], required: false })
  async getSubscriptionPlans(@Query('platform') platform?: 'ios' | 'android') {
    try {
      const subscriptions = this.productConfigService.getSubscriptionPlans();

      if (platform) {
        const filtered = subscriptions.filter(sub =>
          sub.platform === 'both' || sub.platform === platform
        );
        return {
          success: true,
          subscriptions: filtered,
          productIds: this.productConfigService.getSubscriptionProductIds(platform),
        };
      }

      return {
        success: true,
        subscriptions,
      };
    } catch (error) {
      this.logger.error(`Error getting subscription plans: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get subscription plans');
    }
  }

  @Get('products/consumables')
  @ApiOperation({ summary: 'Get consumable products (day refills)' })
  @ApiResponse({ status: 200, description: 'Consumable products retrieved successfully' })
  @ApiQuery({ name: 'platform', enum: ['ios', 'android'], required: false })
  async getConsumableProducts(@Query('platform') platform?: 'ios' | 'android') {
    try {
      const consumables = this.productConfigService.getDayRefillPackages();

      if (platform) {
        const filtered = consumables.filter(consumable =>
          consumable.platform === 'both' || consumable.platform === platform
        );
        return {
          success: true,
          consumables: filtered,
          productIds: this.productConfigService.getConsumableProductIds(platform),
        };
      }

      return {
        success: true,
        consumables,
      };
    } catch (error) {
      this.logger.error(`Error getting consumable products: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get consumable products');
    }
  }

  @Get('config/app-store')
  @ApiOperation({ summary: 'Get App Store Connect configuration guide' })
  @ApiResponse({ status: 200, description: 'Configuration guide retrieved successfully' })
  async getAppStoreConfig() {
    try {
      return {
        success: true,
        guide: this.productConfigService.getAppStoreConnectGuide(),
        products: this.productConfigService.getProductsByPlatform('ios'),
      };
    } catch (error) {
      this.logger.error(`Error getting App Store config: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get App Store configuration');
    }
  }

  @Get('config/google-play')
  @ApiOperation({ summary: 'Get Google Play Console configuration guide' })
  @ApiResponse({ status: 200, description: 'Configuration guide retrieved successfully' })
  async getGooglePlayConfig() {
    try {
      return {
        success: true,
        guide: this.productConfigService.getGooglePlayConsoleGuide(),
        products: this.productConfigService.getProductsByPlatform('android'),
      };
    } catch (error) {
      this.logger.error(`Error getting Google Play config: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get Google Play configuration');
    }
  }

  @Post('subscription/change')
  @ApiOperation({ summary: 'Change subscription plan' })
  @ApiResponse({ status: 200, description: 'Subscription changed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid subscription change request' })
  async changeSubscription(
    @Request() req: RequestWithUser,
    @Body() changeRequest: {
      newPlan: string;
      prorationMode?: 'immediate' | 'next_billing_cycle';
      effectiveDate?: string;
    },
  ) {
    try {
      if (!req.user?.sub) {
        throw new BadRequestException('User authentication required');
      }

      this.logger.log(`Processing subscription change for user ${req.user.sub}`);

      const result = await this.subscriptionLifecycleService.changeSubscription({
        userId: req.user.sub,
        newPlan: changeRequest.newPlan as any,
        prorationMode: changeRequest.prorationMode,
        effectiveDate: changeRequest.effectiveDate ? new Date(changeRequest.effectiveDate) : undefined,
      });

      return result;
    } catch (error) {
      this.logger.error(`Error changing subscription for user ${req.user?.sub}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to change subscription');
    }
  }

  @Post('subscription/cancel')
  @ApiOperation({ summary: 'Cancel subscription' })
  @ApiResponse({ status: 200, description: 'Subscription cancelled successfully' })
  @ApiResponse({ status: 400, description: 'Invalid cancellation request' })
  async cancelSubscription(
    @Request() req: RequestWithUser,
    @Body() cancelRequest: {
      reason?: string;
      effectiveDate?: string;
      refundRequested?: boolean;
    },
  ) {
    try {
      if (!req.user?.sub) {
        throw new BadRequestException('User authentication required');
      }

      this.logger.log(`Processing subscription cancellation for user ${req.user.sub}`);

      const result = await this.subscriptionLifecycleService.cancelSubscription({
        userId: req.user.sub,
        reason: cancelRequest.reason,
        effectiveDate: cancelRequest.effectiveDate ? new Date(cancelRequest.effectiveDate) : undefined,
        refundRequested: cancelRequest.refundRequested,
      });

      return result;
    } catch (error) {
      this.logger.error(`Error cancelling subscription for user ${req.user?.sub}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to cancel subscription');
    }
  }

  @Post('refund')
  @ApiOperation({ summary: 'Process refund request' })
  @ApiResponse({ status: 200, description: 'Refund processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid refund request' })
  async processRefund(
    @Request() req: RequestWithUser,
    @Body() refundRequest: {
      transactionId: string;
      reason: string;
      amount?: number;
    },
  ) {
    try {
      if (!req.user?.sub) {
        throw new BadRequestException('User authentication required');
      }

      this.logger.log(`Processing refund request for user ${req.user.sub}`);

      const result = await this.subscriptionLifecycleService.processRefund({
        userId: req.user.sub,
        transactionId: refundRequest.transactionId,
        reason: refundRequest.reason,
        amount: refundRequest.amount,
      });

      return result;
    } catch (error) {
      this.logger.error(`Error processing refund for user ${req.user?.sub}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to process refund');
    }
  }

  @Post('subscription/reactivate')
  @ApiOperation({ summary: 'Reactivate subscription after failed payment' })
  @ApiResponse({ status: 200, description: 'Subscription reactivated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid reactivation request' })
  async reactivateSubscription(
    @Request() req: RequestWithUser,
    @Body() reactivateRequest: {
      newPlan: string;
    },
  ) {
    try {
      if (!req.user?.sub) {
        throw new BadRequestException('User authentication required');
      }

      this.logger.log(`Processing subscription reactivation for user ${req.user.sub}`);

      const result = await this.subscriptionLifecycleService.reactivateSubscription(
        req.user.sub,
        reactivateRequest.newPlan as any,
      );

      return result;
    } catch (error) {
      this.logger.error(`Error reactivating subscription for user ${req.user?.sub}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to reactivate subscription');
    }
  }

  @Get('billing-history')
  @ApiOperation({ summary: 'Get user billing history' })
  @ApiResponse({ status: 200, description: 'Billing history retrieved successfully' })
  async getBillingHistory(@Request() req: RequestWithUser) {
    try {
      if (!req.user?.sub) {
        throw new BadRequestException('User authentication required');
      }

      this.logger.log(`Getting billing history for user ${req.user.sub}`);

      // Get payment receipts for the user
      const receipts = await this.paymentsService.getUserPaymentHistory(req.user.sub);

      return {
        success: true,
        history: receipts,
      };
    } catch (error) {
      this.logger.error(`Error getting billing history for user ${req.user?.sub}`, error.stack);
      throw new BadRequestException('Failed to get billing history');
    }
  }
}
